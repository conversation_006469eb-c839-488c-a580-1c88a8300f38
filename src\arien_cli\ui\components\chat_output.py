"""
Chat output component for Arien CLI.

This module handles the display of chat messages, tool executions,
and other output in the terminal interface.
"""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional

from rich.console import Group, RenderableType
from rich.markdown import Markdown
from rich.panel import Panel
from rich.syntax import Syntax
from rich.text import Text

from ...utils.output_formatter import output_formatter


class ChatOutputComponent:
    """
    Chat output display component.
    
    Handles:
    - Message rendering with role-based styling
    - Tool execution display
    - Error and help message formatting
    - Message history management
    - Export/import functionality
    """
    
    def __init__(self, max_messages: int = 1000):
        """
        Initialize chat output component.
        
        Args:
            max_messages: Maximum number of messages to keep in history
        """
        self.messages: List[Dict[str, Any]] = []
        self.max_messages = max_messages
        self.width = 80
    
    def add_message(
        self,
        role: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Add a message to the chat output.
        
        Args:
            role: Message role (user, assistant, system, tool)
            content: Message content
            metadata: Additional metadata
        """
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now(),
            "metadata": metadata or {}
        }
        
        self.messages.append(message)
        
        # Trim messages if exceeding max
        if len(self.messages) > self.max_messages:
            self.messages = self.messages[-self.max_messages:]
    
    def add_tool_execution(
        self,
        tool_name: str,
        arguments: Dict[str, Any],
        result: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Add tool execution to the output.
        
        Args:
            tool_name: Name of the executed tool
            arguments: Tool arguments
            result: Tool execution result
        """
        # Add tool call message
        tool_call_content = f"🔧 Executing: {tool_name}"
        if arguments:
            tool_call_content += f"\nArguments: {json.dumps(arguments, indent=2)}"
        
        self.add_message("tool", tool_call_content, {
            "type": "tool_call",
            "tool_name": tool_name,
            "arguments": arguments
        })
        
        # Add result if provided
        if result is not None:
            result_content = output_formatter.format_tool_result(tool_name, result)
            self.add_message("tool", result_content, {
                "type": "tool_result",
                "tool_name": tool_name,
                "result": result
            })
    
    def add_error(self, error: str, suggestions: Optional[List[str]] = None) -> None:
        """
        Add error message.
        
        Args:
            error: Error message
            suggestions: Recovery suggestions
        """
        error_content = output_formatter.format_error(error, suggestions)
        self.add_message("system", error_content, {"type": "error"})
    
    def add_help(self, help_text: str) -> None:
        """
        Add help message.
        
        Args:
            help_text: Help text to display
        """
        self.add_message("system", help_text, {"type": "help"})
    
    def clear(self) -> None:
        """Clear all messages."""
        self.messages.clear()
    
    def set_width(self, width: int) -> None:
        """
        Set display width.
        
        Args:
            width: Display width in characters
        """
        self.width = width
    
    def _render_message(self, message: Dict[str, Any]) -> RenderableType:
        """
        Render a single message.
        
        Args:
            message: Message dictionary
            
        Returns:
            Renderable object
        """
        role = message["role"]
        content = message["content"]
        timestamp = message["timestamp"]
        metadata = message.get("metadata", {})
        
        # Format timestamp
        time_str = timestamp.strftime("%H:%M:%S")
        
        # Role-based styling
        if role == "user":
            title = f"[bold blue]You[/bold blue] [dim]({time_str})[/dim]"
            border_style = "blue"
            content_style = "white"
        elif role == "assistant":
            title = f"[bold green]Arien[/bold green] [dim]({time_str})[/dim]"
            border_style = "green"
            content_style = "white"
        elif role == "system":
            title = f"[bold yellow]System[/bold yellow] [dim]({time_str})[/dim]"
            border_style = "yellow"
            content_style = "yellow"
        elif role == "tool":
            tool_type = metadata.get("type", "tool")
            if tool_type == "tool_call":
                title = f"[bold cyan]Tool Call[/bold cyan] [dim]({time_str})[/dim]"
                border_style = "cyan"
            else:
                title = f"[bold magenta]Tool Result[/bold magenta] [dim]({time_str})[/dim]"
                border_style = "magenta"
            content_style = "white"
        else:
            title = f"[bold]{role.title()}[/bold] [dim]({time_str})[/dim]"
            border_style = "white"
            content_style = "white"
        
        # Process content
        rendered_content = self._process_content(content, role, metadata)
        
        return Panel(
            rendered_content,
            title=title,
            title_align="left",
            border_style=border_style,
            padding=(0, 1),
            width=self.width - 2
        )
    
    def _process_content(
        self,
        content: str,
        role: str,
        metadata: Dict[str, Any]
    ) -> RenderableType:
        """
        Process message content for display.
        
        Args:
            content: Raw content
            role: Message role
            metadata: Message metadata
            
        Returns:
            Processed renderable content
        """
        # Handle different content types
        if role == "assistant":
            # Try to render as markdown for assistant messages
            try:
                return Markdown(content)
            except Exception:
                return Text(content)
        
        elif role == "tool":
            tool_type = metadata.get("type")
            
            if tool_type == "tool_result":
                # Try to format tool results nicely
                tool_name = metadata.get("tool_name")
                result = metadata.get("result", {})
                
                if tool_name == "execute_shell_command":
                    return self._render_command_result(result)
                else:
                    # Generic tool result
                    try:
                        return Syntax(
                            json.dumps(result, indent=2),
                            "json",
                            theme="monokai",
                            line_numbers=False
                        )
                    except Exception:
                        return Text(content)
            else:
                # Tool call or other tool content
                return Text(content)
        
        else:
            # Plain text for other roles
            return Text(content)
    
    def _render_command_result(self, result: Dict[str, Any]) -> RenderableType:
        """
        Render shell command result.
        
        Args:
            result: Command result dictionary
            
        Returns:
            Rendered command result
        """
        elements = []
        
        # Command info
        command = result.get("command", "Unknown")
        exit_code = result.get("exit_code", -1)
        execution_time = result.get("execution_time", 0)
        
        info_text = Text()
        info_text.append("Command: ", style="dim")
        info_text.append(command, style="bold")
        info_text.append(f" | Exit Code: ", style="dim")
        
        if exit_code == 0:
            info_text.append(str(exit_code), style="green")
        else:
            info_text.append(str(exit_code), style="red")
        
        info_text.append(f" | Time: {execution_time:.3f}s", style="dim")
        elements.append(info_text)
        
        # Output
        stdout = result.get("stdout", "").strip()
        stderr = result.get("stderr", "").strip()
        
        if stdout:
            elements.append(Text(""))  # Empty line
            elements.append(Text("Output:", style="bold"))
            
            # Try to detect if output is code/structured data
            if self._looks_like_code(stdout):
                try:
                    elements.append(Syntax(
                        stdout,
                        "bash",
                        theme="monokai",
                        line_numbers=False
                    ))
                except Exception:
                    elements.append(Text(stdout))
            else:
                elements.append(Text(stdout))
        
        if stderr:
            elements.append(Text(""))  # Empty line
            elements.append(Text("Errors:", style="bold red"))
            elements.append(Text(stderr, style="red"))
        
        return Group(*elements)
    
    def _looks_like_code(self, text: str) -> bool:
        """
        Heuristic to detect if text looks like code.
        
        Args:
            text: Text to analyze
            
        Returns:
            True if text looks like code
        """
        code_indicators = [
            "#!/", "import ", "from ", "def ", "class ",
            "function ", "var ", "const ", "let ",
            "{", "}", "[", "]", "=>", "->",
            "<?php", "<html", "<xml"
        ]
        
        return any(indicator in text for indicator in code_indicators)
    
    def render(self) -> RenderableType:
        """
        Render the complete chat output.
        
        Returns:
            Renderable chat output
        """
        if not self.messages:
            return Panel(
                Text("No messages yet. Start a conversation!", style="dim italic"),
                title="Chat",
                border_style="dim"
            )
        
        # Render recent messages (last 20 for performance)
        recent_messages = self.messages[-20:]
        rendered_messages = [
            self._render_message(msg) for msg in recent_messages
        ]
        
        return Group(*rendered_messages)
    
    def export_history(self) -> List[Dict[str, Any]]:
        """
        Export chat history.
        
        Returns:
            List of message dictionaries
        """
        return [
            {
                "role": msg["role"],
                "content": msg["content"],
                "timestamp": msg["timestamp"].isoformat(),
                "metadata": msg["metadata"]
            }
            for msg in self.messages
        ]

    def update_streaming_message(self, content: str) -> None:
        """
        Update the current streaming message.

        Args:
            content: Streaming content
        """
        if self.messages and self.messages[-1]["role"] == "assistant":
            # Update the last assistant message
            self.messages[-1]["content"] = content
            self.messages[-1]["timestamp"] = datetime.now()
        else:
            # Add new streaming message
            self.add_message("assistant", content, {"streaming": True})

    def finalize_streaming_message(self, content: str) -> None:
        """
        Finalize the streaming message.

        Args:
            content: Final content
        """
        if self.messages and self.messages[-1]["role"] == "assistant":
            self.messages[-1]["content"] = content
            self.messages[-1]["metadata"]["streaming"] = False
            self.messages[-1]["timestamp"] = datetime.now()

    def add_tool_preview(self, preview) -> None:
        """
        Add tool call preview.

        Args:
            preview: Tool preview renderable
        """
        self.add_message("system", preview, {"type": "tool_preview"})

    def add_tool_result(self, result) -> None:
        """
        Add tool execution result.

        Args:
            result: Tool result renderable
        """
        self.add_message("system", result, {"type": "tool_result"})

    def clear_messages(self) -> None:
        """Clear all messages."""
        self.messages.clear()
    
    def import_history(self, messages: List[Dict[str, Any]]) -> None:
        """
        Import chat history.
        
        Args:
            messages: List of message dictionaries
        """
        self.messages.clear()
        
        for msg in messages:
            timestamp = msg.get("timestamp")
            if isinstance(timestamp, str):
                timestamp = datetime.fromisoformat(timestamp)
            elif timestamp is None:
                timestamp = datetime.now()
            
            self.messages.append({
                "role": msg["role"],
                "content": msg["content"],
                "timestamp": timestamp,
                "metadata": msg.get("metadata", {})
            })
    
    def get_message_count(self) -> int:
        """Get total message count."""
        return len(self.messages)
    
    def get_last_message(self) -> Optional[Dict[str, Any]]:
        """Get the last message."""
        return self.messages[-1] if self.messages else None
