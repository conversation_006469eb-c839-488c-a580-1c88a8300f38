"""
Header component for Arien CLI.

This module provides the terminal header showing session information,
current working directory, provider, and model.
"""

import os
from datetime import datetime
from typing import Optional

from rich.align import Align
from rich.columns import Columns
from rich.panel import Panel
from rich.text import Text

from ...utils.helpers import get_git_info


class HeaderComponent:
    """
    Terminal header component.
    
    Displays:
    - Session information
    - Current working directory
    - Provider and model
    - Git information (if available)
    - System status
    """
    
    def __init__(self):
        """Initialize header component."""
        self.session_id: Optional[str] = None
        self.provider: Optional[str] = None
        self.model: Optional[str] = None
        self.working_directory: str = os.getcwd()
        self.last_updated = datetime.now()
    
    def update(
        self,
        session_id: Optional[str] = None,
        provider: Optional[str] = None,
        model: Optional[str] = None,
        working_directory: Optional[str] = None
    ) -> None:
        """
        Update header information.
        
        Args:
            session_id: Current session ID
            provider: Current provider
            model: Current model
            working_directory: Current working directory
        """
        if session_id is not None:
            self.session_id = session_id
        if provider is not None:
            self.provider = provider
        if model is not None:
            self.model = model
        if working_directory is not None:
            self.working_directory = working_directory
        
        self.last_updated = datetime.now()
    
    def _format_session_info(self) -> Text:
        """Format session information."""
        text = Text()
        
        if self.session_id:
            session_short = self.session_id[:8] + "..." if len(self.session_id) > 8 else self.session_id
            text.append("Session: ", style="dim")
            text.append(session_short, style="cyan bold")
        else:
            text.append("No Session", style="red dim")
        
        return text
    
    def _format_provider_info(self) -> Text:
        """Format provider and model information."""
        text = Text()
        
        if self.provider:
            text.append("Provider: ", style="dim")
            text.append(self.provider.title(), style="green bold")
            
            if self.model:
                text.append(" | Model: ", style="dim")
                text.append(self.model, style="blue bold")
        else:
            text.append("No Provider", style="red dim")
        
        return text
    
    def _format_directory_info(self) -> Text:
        """Format working directory information."""
        text = Text()
        
        # Get directory name
        dir_name = os.path.basename(self.working_directory) or self.working_directory
        
        text.append("Dir: ", style="dim")
        text.append(dir_name, style="yellow bold")
        
        # Add git info if available
        git_info = get_git_info(self.working_directory)
        if git_info["is_repo"]:
            if git_info["branch"]:
                text.append(" (", style="dim")
                text.append("git:", style="dim")
                text.append(git_info["branch"], style="magenta")
                
                if git_info["status"] == "dirty":
                    text.append("*", style="red")
                
                text.append(")", style="dim")
        
        return text
    
    def _format_status_info(self) -> Text:
        """Format status information."""
        text = Text()
        
        # Current time
        current_time = datetime.now().strftime("%H:%M:%S")
        text.append(current_time, style="dim")
        
        return text
    
    def render(self) -> Panel:
        """
        Render the header component.
        
        Returns:
            Rich Panel with header content
        """
        # Create header sections
        left_section = Columns([
            self._format_session_info(),
            self._format_provider_info()
        ], expand=False)
        
        center_section = Align.center(self._format_directory_info())
        
        right_section = Align.right(self._format_status_info())
        
        # Combine sections
        header_content = Columns([
            left_section,
            center_section,
            right_section
        ], expand=True)
        
        return Panel(
            header_content,
            title="[bold cyan]Arien CLI[/bold cyan]",
            title_align="left",
            border_style="blue",
            height=3
        )
    
    def get_info_dict(self) -> dict:
        """
        Get header information as dictionary.
        
        Returns:
            Dictionary with header information
        """
        return {
            "session_id": self.session_id,
            "provider": self.provider,
            "model": self.model,
            "working_directory": self.working_directory,
            "last_updated": self.last_updated.isoformat(),
            "git_info": get_git_info(self.working_directory)
        }
