"""
Main entry point for Arien CLI.

This module provides the main CLI interface and application startup.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>

from .core.config import config_manager
from .core.session import session_manager
from .ui.terminal_ui import TerminalUI
from .utils.helpers import get_logs_dir

# Initialize Typer app
app = typer.Typer(
    name="arien",
    help="Modern and powerful CLI terminal system with LLM integration",
    add_completion=False
)

# Global console
console = Console()


def setup_logging(log_level: str = "INFO") -> None:
    """
    Setup logging configuration.
    
    Args:
        log_level: Logging level
    """
    logs_dir = get_logs_dir()
    log_file = logs_dir / "arien.log"
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            RichHand<PERSON>(console=console, show_time=False, show_path=False),
            logging.FileHandler(log_file, encoding='utf-8')
        ]
    )
    
    # Reduce noise from external libraries
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)


@app.command()
def chat(
    provider: str = typer.Option("deepseek", "--provider", "-p", help="LLM provider to use"),
    model: str = typer.Option(None, "--model", "-m", help="Model to use"),
    working_dir: str = typer.Option(None, "--dir", "-d", help="Working directory"),
    api_key: str = typer.Option(None, "--api-key", help="API key for the provider"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose logging"),
) -> None:
    """Start interactive chat session."""
    
    # Setup logging
    log_level = "DEBUG" if verbose else "INFO"
    setup_logging(log_level)
    
    # Load configuration
    config = config_manager.load_config()
    
    # Update provider config if API key provided
    if api_key:
        config_manager.update_provider_config(provider, api_key=api_key)
    
    # Determine model
    if not model:
        provider_config = config_manager.get_provider_config(provider)
        if provider_config and provider_config.default_model:
            model = provider_config.default_model
        else:
            model = "deepseek-chat" if provider == "deepseek" else None
    
    # Set working directory
    if working_dir:
        working_dir = os.path.abspath(working_dir)
        if not os.path.exists(working_dir):
            console.print(f"[red]Error: Directory {working_dir} does not exist[/red]")
            raise typer.Exit(1)
    else:
        working_dir = os.getcwd()
    
    # Show startup information
    console.print("[dim]Starting Arien CLI...[/dim]")
    console.print(f"[cyan]Provider:[/cyan] {provider}")
    console.print(f"[cyan]Model:[/cyan] {model}")
    console.print(f"[cyan]Directory:[/cyan] {working_dir}")
    console.print()

    # Start terminal UI
    try:
        terminal_ui = TerminalUI(console=console)
        asyncio.run(terminal_ui.run(
            provider=provider,
            model=model,
            working_directory=working_dir
        ))
    except KeyboardInterrupt:
        console.print("\n[yellow]Goodbye! Thanks for using Arien CLI![/yellow]")
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        if verbose:
            console.print_exception()
        raise typer.Exit(1)


@app.command()
def config(
    list_providers: bool = typer.Option(False, "--list", help="List configured providers"),
    set_provider: str = typer.Option(None, "--set-provider", help="Set provider configuration"),
    api_key: str = typer.Option(None, "--api-key", help="Set API key for provider"),
    base_url: str = typer.Option(None, "--base-url", help="Set base URL for provider"),
    show_config: bool = typer.Option(False, "--show", help="Show current configuration"),
) -> None:
    """Manage configuration."""
    
    config = config_manager.load_config()
    
    if list_providers:
        console.print("[bold]Configured Providers:[/bold]")
        for name, provider_config in config.providers.items():
            status = "✅" if provider_config.enabled else "❌"
            console.print(f"  {status} {name.title()}")
            if provider_config.models:
                models = ", ".join(provider_config.models[:3])
                if len(provider_config.models) > 3:
                    models += f" (+{len(provider_config.models) - 3} more)"
                console.print(f"    Models: {models}")
    
    elif set_provider:
        if set_provider not in ["deepseek", "ollama"]:
            console.print(f"[red]Error: Unsupported provider '{set_provider}'[/red]")
            raise typer.Exit(1)
        
        updates = {}
        if api_key:
            updates["api_key"] = api_key
        if base_url:
            updates["base_url"] = base_url
        
        if updates:
            config_manager.update_provider_config(set_provider, **updates)
            console.print(f"[green]Updated {set_provider} configuration[/green]")
        else:
            console.print("[yellow]No configuration changes specified[/yellow]")
    
    elif show_config:
        console.print("[bold]Current Configuration:[/bold]")
        console.print(f"  Log Level: {config.log_level}")
        console.print(f"  Default Provider: {config.default_session.provider}")
        console.print(f"  Default Model: {config.default_session.model}")
        console.print(f"  Working Directory: {config.default_session.working_directory}")
    
    else:
        console.print("Use --help to see available configuration options")


@app.command()
def providers(
    test: bool = typer.Option(False, "--test", help="Test provider connections"),
    list_models: str = typer.Option(None, "--list-models", help="List models for provider"),
) -> None:
    """Manage LLM providers."""
    
    if test:
        console.print("[bold]Testing provider connections...[/bold]")
        
        # Test each configured provider
        config = config_manager.load_config()
        
        async def test_providers():
            for name, provider_config in config.providers.items():
                console.print(f"Testing {name.title()}...", end=" ")
                
                try:
                    if name == "deepseek":
                        from .core.llm_providers.deepseek import DeepseekProvider
                        provider = DeepseekProvider(
                            api_key=provider_config.api_key,
                            base_url=provider_config.base_url
                        )
                    elif name == "ollama":
                        from .core.llm_providers.ollama import OllamaProvider
                        provider = OllamaProvider(
                            base_url=provider_config.base_url
                        )
                    else:
                        console.print("[red]❌ Unsupported[/red]")
                        continue
                    
                    await provider.initialize()
                    is_valid = await provider.validate_connection()
                    
                    if is_valid:
                        console.print("[green]✅ Connected[/green]")
                    else:
                        console.print("[red]❌ Failed[/red]")
                    
                    await provider.close()
                
                except Exception as e:
                    console.print(f"[red]❌ Error: {e}[/red]")
        
        asyncio.run(test_providers())
    
    elif list_models:
        console.print(f"[bold]Models for {list_models.title()}:[/bold]")
        
        async def list_provider_models():
            config = config_manager.load_config()
            provider_config = config.providers.get(list_models)
            
            if not provider_config:
                console.print(f"[red]Provider {list_models} not configured[/red]")
                return
            
            try:
                if list_models == "deepseek":
                    from .core.llm_providers.deepseek import DeepseekProvider
                    provider = DeepseekProvider(
                        api_key=provider_config.api_key,
                        base_url=provider_config.base_url
                    )
                elif list_models == "ollama":
                    from .core.llm_providers.ollama import OllamaProvider
                    provider = OllamaProvider(
                        base_url=provider_config.base_url
                    )
                else:
                    console.print(f"[red]Unsupported provider: {list_models}[/red]")
                    return
                
                await provider.initialize()
                models = provider.available_models
                
                if models:
                    for model in models:
                        default_marker = " (default)" if model == provider_config.default_model else ""
                        console.print(f"  • {model}{default_marker}")
                else:
                    console.print("  No models available")
                
                await provider.close()
            
            except Exception as e:
                console.print(f"[red]Error: {e}[/red]")
        
        asyncio.run(list_provider_models())
    
    else:
        console.print("Use --help to see available provider options")


@app.command()
def version() -> None:
    """Show version information."""
    from . import __version__
    
    console.print(f"[bold cyan]Arien CLI[/bold cyan] version [green]{__version__}[/green]")
    console.print("Modern and powerful CLI terminal system with LLM integration")


@app.command()
def setup() -> None:
    """Run initial setup wizard."""
    from .ui.components.onboarding import OnboardingComponent
    
    console.print("[bold cyan]Welcome to Arien CLI![/bold cyan]")
    console.print("Let's set up your configuration...\n")
    
    onboarding = OnboardingComponent(console)
    asyncio.run(onboarding.run())


def main() -> None:
    """Main entry point."""
    try:
        app()
    except KeyboardInterrupt:
        console.print("\n[yellow]Interrupted by user[/yellow]")
        sys.exit(0)
    except Exception as e:
        console.print(f"[red]Unexpected error: {e}[/red]")
        sys.exit(1)


if __name__ == "__main__":
    main()
