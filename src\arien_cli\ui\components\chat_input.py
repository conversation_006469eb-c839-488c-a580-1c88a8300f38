"""
Chat input component for Arien CLI.

This module provides the chat input interface with support for
multi-line input, command completion, and input validation.
"""

from typing import Optional

from rich.panel import Panel
from rich.text import Text


class ChatInputComponent:
    """
    Chat input component.
    
    Provides:
    - Input prompt display
    - Multi-line input support
    - Command completion hints
    - Input validation
    """
    
    def __init__(self):
        """Initialize chat input component."""
        self.prompt = "You: "
        self.placeholder = "Type your message here..."
        self.width = 80
        self.current_input = ""
        self.is_multiline = False
    
    def set_prompt(self, prompt: str) -> None:
        """
        Set input prompt.
        
        Args:
            prompt: Prompt text
        """
        self.prompt = prompt
    
    def set_placeholder(self, placeholder: str) -> None:
        """
        Set placeholder text.
        
        Args:
            placeholder: Placeholder text
        """
        self.placeholder = placeholder
    
    def set_width(self, width: int) -> None:
        """
        Set input width.
        
        Args:
            width: Input width in characters
        """
        self.width = width
    
    def set_input(self, text: str) -> None:
        """
        Set current input text.
        
        Args:
            text: Input text
        """
        self.current_input = text
    
    def clear_input(self) -> None:
        """Clear current input."""
        self.current_input = ""
    
    def toggle_multiline(self) -> None:
        """Toggle multiline input mode."""
        self.is_multiline = not self.is_multiline
    
    def render(self) -> Panel:
        """
        Render the input component.
        
        Returns:
            Rich Panel with input interface
        """
        # Create input text
        input_text = Text()
        
        # Add prompt
        input_text.append(self.prompt, style="bold cyan")
        
        # Add current input or placeholder
        if self.current_input:
            input_text.append(self.current_input)
            
            # Add cursor indicator
            input_text.append("█", style="reverse")
        else:
            input_text.append(self.placeholder, style="dim italic")
        
        # Add multiline indicator
        if self.is_multiline:
            input_text.append("\n[Ctrl+Enter to send, Ctrl+M to toggle multiline]", style="dim")
        else:
            input_text.append(" [Enter to send, Ctrl+M for multiline]", style="dim")
        
        return Panel(
            input_text,
            title="Input",
            title_align="left",
            border_style="cyan",
            height=3 if not self.is_multiline else 5,
            width=self.width
        )
    
    def get_completion_hints(self, text: str) -> list:
        """
        Get completion hints for input text.
        
        Args:
            text: Current input text
            
        Returns:
            List of completion suggestions
        """
        hints = []
        
        # Slash command completion
        if text.startswith('/'):
            commands = [
                '/help', '/clear', '/exit', '/model', '/provider', 
                '/session', '/history', '/export', '/import'
            ]
            
            for cmd in commands:
                if cmd.startswith(text):
                    hints.append(cmd)
        
        # Common shell command completion
        elif text.strip():
            common_commands = [
                'ls', 'cd', 'pwd', 'mkdir', 'rmdir', 'cp', 'mv', 'rm',
                'cat', 'head', 'tail', 'grep', 'find', 'which', 'whereis',
                'ps', 'top', 'kill', 'jobs', 'bg', 'fg',
                'git status', 'git add', 'git commit', 'git push', 'git pull',
                'python', 'pip install', 'npm install', 'node', 'docker',
                'curl', 'wget', 'ssh', 'scp', 'rsync'
            ]
            
            text_lower = text.lower()
            for cmd in common_commands:
                if cmd.startswith(text_lower):
                    hints.append(cmd)
        
        return hints[:5]  # Limit to 5 suggestions
    
    def validate_input(self, text: str) -> tuple[bool, Optional[str]]:
        """
        Validate input text.
        
        Args:
            text: Input text to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not text.strip():
            return False, "Input cannot be empty"
        
        # Check for potentially dangerous commands
        dangerous_patterns = [
            'rm -rf /',
            'sudo rm',
            'dd if=',
            'mkfs.',
            'format c:',
            'del /s /q',
            'shutdown',
            'reboot'
        ]
        
        text_lower = text.lower()
        for pattern in dangerous_patterns:
            if pattern in text_lower:
                return False, f"Potentially dangerous command detected: {pattern}"
        
        # Check length
        if len(text) > 10000:
            return False, "Input too long (max 10,000 characters)"
        
        return True, None
    
    def format_input_for_display(self, text: str, max_length: int = 100) -> str:
        """
        Format input text for display.
        
        Args:
            text: Input text
            max_length: Maximum display length
            
        Returns:
            Formatted text
        """
        if len(text) <= max_length:
            return text
        
        # Truncate and add ellipsis
        return text[:max_length - 3] + "..."
    
    def get_input_stats(self) -> dict:
        """
        Get input statistics.
        
        Returns:
            Dictionary with input stats
        """
        return {
            "length": len(self.current_input),
            "lines": self.current_input.count('\n') + 1 if self.current_input else 0,
            "words": len(self.current_input.split()) if self.current_input else 0,
            "is_multiline": self.is_multiline,
            "is_command": self.current_input.startswith('/') if self.current_input else False
        }
