"""
Base LLM Provider interface for Arien CLI.

This module defines the abstract base class that all LLM providers must implement.
It provides a consistent interface for interacting with different LLM services.
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Any, AsyncGenerator, Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

from pydantic import BaseModel


class MessageRole(str, Enum):
    """Message roles for chat conversations."""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"
    TOOL = "tool"


@dataclass
class ChatMessage:
    """Represents a single chat message."""
    role: MessageRole
    content: str
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_call_id: Optional[str] = None
    name: Optional[str] = None


@dataclass
class ToolCall:
    """Represents a tool call from the LLM."""
    id: str
    name: str
    arguments: Dict[str, Any]


@dataclass
class StreamChunk:
    """Represents a chunk of streamed response."""
    content: Optional[str] = None
    tool_calls: Optional[List[ToolCall]] = None
    finish_reason: Optional[str] = None
    usage: Optional[Dict[str, Any]] = None


class LLMResponse(BaseModel):
    """Response from LLM provider."""
    content: str
    tool_calls: List[ToolCall] = []
    usage: Optional[Dict[str, Any]] = None
    finish_reason: Optional[str] = None
    model: Optional[str] = None


class LLMProvider(ABC):
    """
    Abstract base class for LLM providers.
    
    This class defines the interface that all LLM providers must implement
    to work with the Arien CLI system.
    """
    
    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        """
        Initialize the LLM provider.
        
        Args:
            api_key: API key for the provider (if required)
            base_url: Base URL for the provider API (if different from default)
        """
        self.api_key = api_key
        self.base_url = base_url
        self._client = None
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Return the name of the provider."""
        pass
    
    @property
    @abstractmethod
    def available_models(self) -> List[str]:
        """Return list of available models for this provider."""
        pass
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the provider (setup client, validate credentials, etc.)."""
        pass
    
    @abstractmethod
    async def chat_completion(
        self,
        messages: List[ChatMessage],
        model: str,
        tools: Optional[List[Dict[str, Any]]] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[LLMResponse, AsyncGenerator[StreamChunk, None]]:
        """
        Generate a chat completion.
        
        Args:
            messages: List of chat messages
            model: Model to use for completion
            tools: Available tools for function calling
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            stream: Whether to stream the response
            **kwargs: Additional provider-specific parameters
            
        Returns:
            LLMResponse for non-streaming, AsyncGenerator for streaming
        """
        pass
    
    @abstractmethod
    async def validate_connection(self) -> bool:
        """
        Validate connection to the provider.
        
        Returns:
            True if connection is valid, False otherwise
        """
        pass
    
    async def close(self) -> None:
        """Close the provider connection."""
        if hasattr(self._client, 'close'):
            await self._client.close()
    
    def __str__(self) -> str:
        return f"{self.name} LLM Provider"
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(name='{self.name}')>"
