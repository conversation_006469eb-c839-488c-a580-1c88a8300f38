"""
Tool Registry for Arien CLI.

This module manages the registration and execution of tools available to the LLM.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Type, Union

from .shell_tool import ShellTool, CommandContext, ExecutionMode

logger = logging.getLogger(__name__)


class ToolRegistry:
    """
    Registry for managing and executing tools.
    
    This class provides a centralized way to register tools and execute them
    based on LLM function calls.
    """
    
    def __init__(self):
        """Initialize the tool registry."""
        self._tools: Dict[str, Any] = {}
        self._tool_definitions: List[Dict[str, Any]] = []
        
        # Register built-in tools
        self._register_builtin_tools()
    
    def _register_builtin_tools(self) -> None:
        """Register built-in tools."""
        # Register shell tool
        shell_tool = ShellTool()
        self.register_tool("execute_shell_command", shell_tool.execute_command, shell_tool.get_tool_definition())
    
    def register_tool(
        self, 
        name: str, 
        handler: callable, 
        definition: Dict[str, Any]
    ) -> None:
        """
        Register a new tool.
        
        Args:
            name: Tool name (must match function name in definition)
            handler: Async function to handle tool execution
            definition: Tool definition in OpenAI function calling format
        """
        self._tools[name] = handler
        self._tool_definitions.append(definition)
        logger.info(f"Registered tool: {name}")
    
    def get_tool_definitions(self) -> List[Dict[str, Any]]:
        """Get all tool definitions for LLM function calling."""
        return self._tool_definitions.copy()
    
    async def execute_tool(
        self, 
        name: str, 
        arguments: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute a tool by name with given arguments.
        
        Args:
            name: Tool name
            arguments: Tool arguments
            
        Returns:
            Tool execution result
        """
        if name not in self._tools:
            return {
                "success": False,
                "error": f"Tool '{name}' not found",
                "available_tools": list(self._tools.keys())
            }
        
        try:
            handler = self._tools[name]
            
            # Handle shell command execution specially
            if name == "execute_shell_command":
                return await self._execute_shell_command(arguments)
            
            # For other tools, call directly
            result = await handler(**arguments)
            
            return {
                "success": True,
                "result": result
            }
        
        except Exception as e:
            logger.error(f"Tool execution failed for {name}: {e}")
            return {
                "success": False,
                "error": str(e),
                "tool": name,
                "arguments": arguments
            }
    
    async def _execute_shell_command(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute shell command with proper argument handling."""
        command = arguments.get("command")
        if not command:
            return {
                "success": False,
                "error": "Command is required"
            }
        
        # Create shell tool instance
        shell_tool = ShellTool(arguments.get("working_directory"))
        
        # Prepare context
        context = CommandContext(
            working_directory=arguments.get("working_directory", shell_tool.working_directory),
            environment=arguments.get("environment", shell_tool.environment),
            timeout=arguments.get("timeout")
        )
        
        # Parse execution mode
        mode_str = arguments.get("mode", "sequential")
        try:
            mode = ExecutionMode(mode_str)
        except ValueError:
            mode = ExecutionMode.SEQUENTIAL
        
        # Execute command
        result = await shell_tool.execute_command(command, context, mode)
        
        return {
            "success": result.exit_code == 0,
            "command": result.command,
            "exit_code": result.exit_code,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "execution_time": result.execution_time,
            "working_directory": result.working_directory,
            "process_id": result.process_id,
            "safety_level": result.safety_level.value if result.safety_level else None
        }
    
    def get_tool_usage_guide(self) -> str:
        """Get comprehensive usage guide for all tools."""
        guide = """
# Arien CLI Tool Usage Guide

## Available Tools

### 1. Shell Command Execution (`execute_shell_command`)

**Purpose**: Execute shell commands with comprehensive safety checks and output processing.

**When to Use**:
- File system operations (ls, find, grep, cat, head, tail, etc.)
- Package management (pip install, npm install, apt update, brew install, etc.)
- Development tasks (git operations, build commands, testing, etc.)
- System information gathering (ps, df, free, uname, etc.)
- Text processing and data manipulation (awk, sed, sort, uniq, etc.)

**Execution Modes**:

1. **Sequential Mode** (default):
   - Use when commands depend on previous results
   - Installation procedures that must complete in order
   - File operations that must finish before next step
   - Database migrations or schema changes
   
2. **Parallel Mode**:
   - Independent file processing tasks
   - Multiple downloads or API calls
   - Batch operations on different directories
   - Parallel testing or validation
   
3. **Interactive Mode**:
   - Commands requiring user input
   - Interactive shells or REPLs
   - Configuration wizards
   
4. **Background Mode**:
   - Long-running processes
   - Server startup
   - Monitoring tasks

**Safety Levels**:
- **SAFE**: Read-only operations, information gathering
- **MODERATE**: File operations, package management
- **DANGEROUS**: System modifications, user management
- **CRITICAL**: Potentially system-breaking operations (blocked)

**Examples**:

```json
{
  "command": "ls -la",
  "mode": "sequential"
}
```

```json
{
  "command": "pip install requests",
  "working_directory": "/path/to/project",
  "mode": "sequential",
  "timeout": 300
}
```

```json
{
  "command": "find . -name '*.py' | head -10",
  "mode": "sequential"
}
```

**Output Processing**:
- Captures both stdout and stderr
- Provides execution time and exit codes
- Monitors resource usage for background processes
- Formats output for easy parsing and display

**Best Practices**:
1. Always check exit codes for error handling
2. Use appropriate timeouts for long-running commands
3. Specify working directory when needed
4. Use parallel mode for independent operations
5. Monitor background processes appropriately

**When NOT to Use**:
- Commands requiring elevated privileges without explicit approval
- Potentially destructive operations without confirmation
- Commands that might compromise system security
- Operations that could consume excessive resources

## Error Handling

All tools return structured results with success indicators:
- `success`: Boolean indicating if operation succeeded
- `error`: Error message if operation failed
- Additional tool-specific result data

## Resource Management

- Commands have configurable timeouts
- Background processes are tracked and can be managed
- Resource usage is monitored for long-running operations
- Automatic cleanup of failed or abandoned processes
        """
        
        return guide.strip()


# Global tool registry instance
tool_registry = ToolRegistry()
