#!/usr/bin/env python3
"""
Universal installer for Arien CLI.

This script provides installation, update, and uninstallation functionality
for Windows 11 WSL, macOS, and Linux systems.
"""

import argparse
import os
import platform
import shutil
import subprocess
import sys
import tempfile
import urllib.request
from pathlib import Path
from typing import List, Optional


class ArienInstaller:
    """Universal installer for Arien CLI."""
    
    def __init__(self):
        """Initialize the installer."""
        self.system = platform.system().lower()
        self.is_wsl = self._detect_wsl()
        self.python_cmd = self._find_python()
        self.pip_cmd = self._find_pip()
        
        # Installation paths
        if self.system == "windows" or self.is_wsl:
            self.install_dir = Path.home() / ".local" / "bin"
            self.config_dir = Path.home() / ".config" / "arien-cli"
        else:
            self.install_dir = Path("/usr/local/bin")
            self.config_dir = Path.home() / ".config" / "arien-cli"
        
        self.venv_dir = Path.home() / ".local" / "share" / "arien-cli"
    
    def _detect_wsl(self) -> bool:
        """Detect if running in WSL."""
        try:
            with open("/proc/version", "r") as f:
                return "microsoft" in f.read().lower()
        except FileNotFoundError:
            return False
    
    def _find_python(self) -> str:
        """Find Python executable."""
        for cmd in ["python3", "python"]:
            if shutil.which(cmd):
                try:
                    result = subprocess.run(
                        [cmd, "--version"],
                        capture_output=True,
                        text=True,
                        check=True
                    )
                    version = result.stdout.strip()
                    if "Python 3." in version:
                        return cmd
                except subprocess.CalledProcessError:
                    continue
        
        raise RuntimeError("Python 3.8+ is required but not found")
    
    def _find_pip(self) -> str:
        """Find pip executable."""
        for cmd in ["pip3", "pip"]:
            if shutil.which(cmd):
                return cmd
        
        raise RuntimeError("pip is required but not found")
    
    def _run_command(self, cmd: List[str], check: bool = True) -> subprocess.CompletedProcess:
        """Run a command and return the result."""
        print(f"Running: {' '.join(cmd)}")
        return subprocess.run(cmd, check=check, capture_output=True, text=True)
    
    def _create_venv(self) -> None:
        """Create virtual environment."""
        print("Creating virtual environment...")
        
        if self.venv_dir.exists():
            print("Removing existing virtual environment...")
            shutil.rmtree(self.venv_dir)
        
        self.venv_dir.parent.mkdir(parents=True, exist_ok=True)
        
        self._run_command([
            self.python_cmd, "-m", "venv", str(self.venv_dir)
        ])
        
        print(f"Virtual environment created at {self.venv_dir}")
    
    def _get_venv_python(self) -> str:
        """Get Python executable in virtual environment."""
        if self.system == "windows":
            return str(self.venv_dir / "Scripts" / "python.exe")
        else:
            return str(self.venv_dir / "bin" / "python")
    
    def _get_venv_pip(self) -> str:
        """Get pip executable in virtual environment."""
        if self.system == "windows":
            return str(self.venv_dir / "Scripts" / "pip.exe")
        else:
            return str(self.venv_dir / "bin" / "pip")
    
    def _install_package(self, source: str = ".") -> None:
        """Install the package."""
        print("Installing Arien CLI...")
        
        venv_pip = self._get_venv_pip()
        
        # Upgrade pip
        self._run_command([venv_pip, "install", "--upgrade", "pip"])
        
        # Install package
        if source == ".":
            # Local installation
            self._run_command([venv_pip, "install", "-e", source])
        else:
            # Remote installation
            self._run_command([venv_pip, "install", source])
        
        print("Package installed successfully")
    
    def _create_launcher_script(self) -> None:
        """Create launcher script."""
        print("Creating launcher script...")
        
        self.install_dir.mkdir(parents=True, exist_ok=True)
        
        venv_python = self._get_venv_python()
        launcher_path = self.install_dir / "arien"
        
        if self.system == "windows":
            # Create batch file for Windows
            launcher_content = f"""@echo off
"{venv_python}" -m arien_cli.main %*
"""
            launcher_path = launcher_path.with_suffix(".bat")
        else:
            # Create shell script for Unix-like systems
            launcher_content = f"""#!/bin/bash
exec "{venv_python}" -m arien_cli.main "$@"
"""
        
        with open(launcher_path, "w") as f:
            f.write(launcher_content)
        
        if self.system != "windows":
            os.chmod(launcher_path, 0o755)
        
        print(f"Launcher script created at {launcher_path}")
    
    def _update_path(self) -> None:
        """Update PATH environment variable."""
        print("Updating PATH...")
        
        if self.system == "windows":
            # For Windows, we'll just inform the user
            print(f"Please add {self.install_dir} to your PATH environment variable")
        else:
            # For Unix-like systems, update shell profile
            shell_profiles = [
                Path.home() / ".bashrc",
                Path.home() / ".zshrc",
                Path.home() / ".profile"
            ]
            
            path_line = f'export PATH="$PATH:{self.install_dir}"'
            
            for profile in shell_profiles:
                if profile.exists():
                    with open(profile, "r") as f:
                        content = f.read()
                    
                    if str(self.install_dir) not in content:
                        with open(profile, "a") as f:
                            f.write(f"\n# Added by Arien CLI installer\n{path_line}\n")
                        print(f"Updated {profile}")
    
    def install(self, source: str = ".") -> None:
        """Install Arien CLI."""
        print("Installing Arien CLI...")
        print(f"System: {self.system}")
        print(f"WSL: {self.is_wsl}")
        print(f"Python: {self.python_cmd}")
        print(f"Install directory: {self.install_dir}")
        print()
        
        try:
            self._create_venv()
            self._install_package(source)
            self._create_launcher_script()
            self._update_path()
            
            print("\n✅ Installation completed successfully!")
            print(f"Run 'arien setup' to configure your providers")
            print(f"Run 'arien chat' to start chatting")
            
            if self.system != "windows":
                print("\nNote: You may need to restart your shell or run:")
                print(f"  source ~/.bashrc  # or ~/.zshrc")
        
        except Exception as e:
            print(f"\n❌ Installation failed: {e}")
            sys.exit(1)
    
    def update(self) -> None:
        """Update existing installation."""
        print("Updating Arien CLI...")
        
        if not self.venv_dir.exists():
            print("Arien CLI is not installed. Run install first.")
            sys.exit(1)
        
        try:
            venv_pip = self._get_venv_pip()
            
            # Update package
            self._run_command([venv_pip, "install", "--upgrade", "arien-cli"])
            
            print("\n✅ Update completed successfully!")
        
        except Exception as e:
            print(f"\n❌ Update failed: {e}")
            sys.exit(1)
    
    def uninstall(self) -> None:
        """Uninstall Arien CLI."""
        print("Uninstalling Arien CLI...")
        
        try:
            # Remove virtual environment
            if self.venv_dir.exists():
                print(f"Removing virtual environment: {self.venv_dir}")
                shutil.rmtree(self.venv_dir)
            
            # Remove launcher script
            launcher_paths = [
                self.install_dir / "arien",
                self.install_dir / "arien.bat"
            ]
            
            for launcher_path in launcher_paths:
                if launcher_path.exists():
                    print(f"Removing launcher: {launcher_path}")
                    launcher_path.unlink()
            
            # Remove configuration (optional)
            if self.config_dir.exists():
                response = input(f"Remove configuration directory {self.config_dir}? (y/N): ")
                if response.lower() in ['y', 'yes']:
                    shutil.rmtree(self.config_dir)
                    print("Configuration removed")
            
            print("\n✅ Uninstallation completed successfully!")
        
        except Exception as e:
            print(f"\n❌ Uninstallation failed: {e}")
            sys.exit(1)
    
    def status(self) -> None:
        """Show installation status."""
        print("Arien CLI Installation Status")
        print("=" * 30)
        
        # Check virtual environment
        venv_exists = self.venv_dir.exists()
        print(f"Virtual Environment: {'✅' if venv_exists else '❌'} {self.venv_dir}")
        
        # Check launcher script
        launcher_paths = [
            self.install_dir / "arien",
            self.install_dir / "arien.bat"
        ]
        
        launcher_exists = any(p.exists() for p in launcher_paths)
        print(f"Launcher Script: {'✅' if launcher_exists else '❌'}")
        
        # Check if arien command is available
        arien_available = shutil.which("arien") is not None
        print(f"Command Available: {'✅' if arien_available else '❌'}")
        
        # Check configuration
        config_exists = self.config_dir.exists()
        print(f"Configuration: {'✅' if config_exists else '❌'} {self.config_dir}")
        
        if venv_exists and launcher_exists and arien_available:
            print("\n✅ Arien CLI is properly installed")
        else:
            print("\n❌ Arien CLI installation is incomplete")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Universal installer for Arien CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python install.py install          # Install from current directory
  python install.py install --remote # Install from PyPI (when available)
  python install.py update           # Update existing installation
  python install.py uninstall        # Remove installation
  python install.py status           # Show installation status
        """
    )
    
    parser.add_argument(
        "action",
        choices=["install", "update", "uninstall", "status"],
        help="Action to perform"
    )
    
    parser.add_argument(
        "--remote",
        action="store_true",
        help="Install from remote repository (PyPI)"
    )
    
    args = parser.parse_args()
    
    installer = ArienInstaller()
    
    if args.action == "install":
        source = "arien-cli" if args.remote else "."
        installer.install(source)
    elif args.action == "update":
        installer.update()
    elif args.action == "uninstall":
        installer.uninstall()
    elif args.action == "status":
        installer.status()


if __name__ == "__main__":
    main()
