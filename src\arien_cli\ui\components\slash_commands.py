"""
Slash commands component for Arien CLI.

This module handles slash command processing and execution.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Tuple

from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.text import Text

from ...core.config import config_manager
from ...core.session import session_manager

logger = logging.getLogger(__name__)


class SlashCommandsComponent:
    """
    Slash commands processor.
    
    Handles:
    - Command parsing and validation
    - Command execution
    - Help and documentation
    - Session management commands
    - Configuration commands
    """
    
    def __init__(self, console: Optional[Console] = None):
        """
        Initialize slash commands component.
        
        Args:
            console: Rich console instance
        """
        self.console = console or Console()
        self.commands = {
            "help": self._cmd_help,
            "clear": self._cmd_clear,
            "exit": self._cmd_exit,
            "model": self._cmd_model,
            "provider": self._cmd_provider,
            "session": self._cmd_session,
            "history": self._cmd_history,
            "export": self._cmd_export,
            "import": self._cmd_import,
            "config": self._cmd_config,
            "status": self._cmd_status,
            "debug": self._cmd_debug
        }
    
    def is_slash_command(self, text: str) -> bool:
        """
        Check if text is a slash command.
        
        Args:
            text: Input text
            
        Returns:
            True if text starts with '/'
        """
        return text.strip().startswith('/')
    
    def parse_command(self, text: str) -> Tuple[str, List[str]]:
        """
        Parse slash command and arguments.
        
        Args:
            text: Command text
            
        Returns:
            Tuple of (command, arguments)
        """
        text = text.strip()
        if not text.startswith('/'):
            raise ValueError("Not a slash command")
        
        parts = text[1:].split()
        command = parts[0] if parts else ""
        args = parts[1:] if len(parts) > 1 else []
        
        return command, args
    
    async def execute_command(self, text: str) -> Dict[str, Any]:
        """
        Execute a slash command.
        
        Args:
            text: Command text
            
        Returns:
            Command execution result
        """
        try:
            command, args = self.parse_command(text)
            
            if command not in self.commands:
                return {
                    "success": False,
                    "error": f"Unknown command: /{command}",
                    "suggestions": self._get_command_suggestions(command)
                }
            
            handler = self.commands[command]
            result = await handler(args)
            
            return {
                "success": True,
                "result": result,
                "command": command,
                "args": args
            }
        
        except Exception as e:
            logger.error(f"Error executing slash command: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _get_command_suggestions(self, command: str) -> List[str]:
        """Get command suggestions for unknown command."""
        suggestions = []
        for cmd in self.commands.keys():
            if cmd.startswith(command) or command in cmd:
                suggestions.append(f"/{cmd}")
        return suggestions[:3]  # Limit to 3 suggestions
    
    async def _cmd_help(self, args: List[str]) -> str:
        """Show help information."""
        if args and args[0] in self.commands:
            # Show specific command help
            command = args[0]
            return self._get_command_help(command)
        
        # Show general help
        help_table = Table(title="Available Slash Commands")
        help_table.add_column("Command", style="cyan", no_wrap=True)
        help_table.add_column("Description", style="white")
        help_table.add_column("Usage", style="dim")
        
        commands_info = [
            ("help", "Show this help or help for specific command", "/help [command]"),
            ("clear", "Clear chat history", "/clear"),
            ("exit", "Exit the application", "/exit"),
            ("model", "Change or show current model", "/model [model_name]"),
            ("provider", "Change or show current provider", "/provider [provider_name]"),
            ("session", "Session management", "/session [new|list|switch|delete] [id]"),
            ("history", "Show message history", "/history [limit]"),
            ("export", "Export chat history", "/export [filename]"),
            ("import", "Import chat history", "/import <filename>"),
            ("config", "Configuration management", "/config [show|set|reset]"),
            ("status", "Show system status", "/status"),
            ("debug", "Debug information", "/debug [on|off]")
        ]
        
        for cmd, desc, usage in commands_info:
            help_table.add_row(cmd, desc, usage)
        
        return help_table
    
    def _get_command_help(self, command: str) -> str:
        """Get detailed help for specific command."""
        help_text = {
            "help": "Show help information.\nUsage: /help [command]\nExamples:\n  /help\n  /help model",
            "clear": "Clear the current chat history.\nUsage: /clear\nThis will remove all messages from the current session.",
            "exit": "Exit the Arien CLI application.\nUsage: /exit\nAlternatives: Ctrl+C, Ctrl+D",
            "model": "Change or display the current model.\nUsage: /model [model_name]\nExamples:\n  /model (show current)\n  /model deepseek-chat\n  /model deepseek-reasoner",
            "provider": "Change or display the current provider.\nUsage: /provider [provider_name]\nExamples:\n  /provider (show current)\n  /provider deepseek\n  /provider ollama",
            "session": "Manage chat sessions.\nUsage: /session [action] [id]\nActions:\n  new - Create new session\n  list - List all sessions\n  switch <id> - Switch to session\n  delete <id> - Delete session",
            "history": "Show message history.\nUsage: /history [limit]\nExamples:\n  /history (show all)\n  /history 10 (show last 10)",
            "export": "Export chat history to file.\nUsage: /export [filename]\nFormats: JSON, Markdown\nExample: /export chat_backup.json",
            "import": "Import chat history from file.\nUsage: /import <filename>\nExample: /import chat_backup.json",
            "config": "Configuration management.\nUsage: /config [action]\nActions:\n  show - Show current config\n  set <key> <value> - Set config value\n  reset - Reset to defaults",
            "status": "Show system status and diagnostics.\nUsage: /status\nShows: providers, models, session info, system resources",
            "debug": "Toggle debug mode.\nUsage: /debug [on|off]\nExample: /debug on"
        }
        
        return help_text.get(command, f"No detailed help available for /{command}")
    
    async def _cmd_clear(self, args: List[str]) -> str:
        """Clear chat history."""
        session = session_manager.get_active_session()
        if session:
            session.clear_messages()
            return "Chat history cleared."
        return "No active session to clear."
    
    async def _cmd_exit(self, args: List[str]) -> str:
        """Exit the application."""
        return "exit_requested"
    
    async def _cmd_model(self, args: List[str]) -> str:
        """Change or show current model."""
        session = session_manager.get_active_session()
        if not session:
            return "No active session."
        
        if not args:
            # Show current model
            return f"Current model: {session.model}"
        
        # Change model
        new_model = args[0]
        
        # Validate model is available for current provider
        if session.provider:
            available_models = session.provider.available_models
            if new_model not in available_models:
                return f"Model '{new_model}' not available for {session.provider.name}.\nAvailable models: {', '.join(available_models)}"
        
        session.model = new_model
        return f"Model changed to: {new_model}"
    
    async def _cmd_provider(self, args: List[str]) -> str:
        """Change or show current provider."""
        session = session_manager.get_active_session()
        if not session:
            return "No active session."
        
        if not args:
            # Show current provider
            provider_name = session.provider.name if session.provider else "None"
            return f"Current provider: {provider_name}"
        
        # Change provider
        new_provider = args[0]
        config = config_manager.load_config()
        
        if new_provider not in config.providers:
            available = list(config.providers.keys())
            return f"Provider '{new_provider}' not configured.\nAvailable providers: {', '.join(available)}"
        
        # Switch provider
        await session.switch_provider(new_provider)
        return f"Provider changed to: {new_provider}"
    
    async def _cmd_session(self, args: List[str]) -> str:
        """Session management."""
        if not args:
            # Show current session info
            session = session_manager.get_active_session()
            if session:
                return f"Current session: {session.id}\nProvider: {session.provider_name}\nModel: {session.model}\nMessages: {len(session.messages)}"
            return "No active session."
        
        action = args[0]
        
        if action == "new":
            session = session_manager.create_session()
            return f"Created new session: {session.id}"
        
        elif action == "list":
            sessions = session_manager.list_sessions()
            if not sessions:
                return "No sessions available."
            
            session_list = []
            for session_info in sessions:
                active = " (active)" if session_info.id == session_manager.active_session_id else ""
                session_list.append(f"  {session_info.id}: {session_info.name}{active}")
            
            return "Sessions:\n" + "\n".join(session_list)
        
        elif action == "switch":
            if len(args) < 2:
                return "Usage: /session switch <session_id>"
            
            session_id = args[1]
            session = session_manager.get_session(session_id)
            if not session:
                return f"Session '{session_id}' not found."
            
            session_manager.set_active_session(session_id)
            return f"Switched to session: {session_id}"
        
        elif action == "delete":
            if len(args) < 2:
                return "Usage: /session delete <session_id>"
            
            session_id = args[1]
            if session_manager.delete_session(session_id):
                return f"Deleted session: {session_id}"
            return f"Session '{session_id}' not found."
        
        else:
            return f"Unknown session action: {action}\nAvailable actions: new, list, switch, delete"
    
    async def _cmd_history(self, args: List[str]) -> str:
        """Show message history."""
        session = session_manager.get_active_session()
        if not session:
            return "No active session."
        
        limit = None
        if args:
            try:
                limit = int(args[0])
            except ValueError:
                return "Invalid limit. Please provide a number."
        
        messages = session.get_messages(limit=limit)
        if not messages:
            return "No messages in history."
        
        history_text = []
        for msg in messages:
            timestamp = msg.timestamp.strftime("%H:%M:%S")
            role = msg.role.value.title()
            content_preview = msg.content[:100] + "..." if len(msg.content) > 100 else msg.content
            history_text.append(f"[{timestamp}] {role}: {content_preview}")
        
        return "\n".join(history_text)
    
    async def _cmd_export(self, args: List[str]) -> str:
        """Export chat history."""
        session = session_manager.get_active_session()
        if not session:
            return "No active session to export."
        
        filename = args[0] if args else f"arien_chat_{session.id[:8]}.json"
        
        try:
            await session.export_to_file(filename)
            return f"Chat history exported to: {filename}"
        except Exception as e:
            return f"Export failed: {e}"
    
    async def _cmd_import(self, args: List[str]) -> str:
        """Import chat history."""
        if not args:
            return "Usage: /import <filename>"
        
        filename = args[0]
        session = session_manager.get_active_session()
        if not session:
            return "No active session to import into."
        
        try:
            await session.import_from_file(filename)
            return f"Chat history imported from: {filename}"
        except Exception as e:
            return f"Import failed: {e}"
    
    async def _cmd_config(self, args: List[str]) -> str:
        """Configuration management."""
        if not args:
            # Show current config
            config = config_manager.load_config()
            config_text = []
            config_text.append("Current Configuration:")
            config_text.append(f"  Log Level: {config.log_level}")
            config_text.append(f"  UI Theme: {config.ui.theme}")
            config_text.append(f"  Stream Output: {config.ui.stream_output}")
            config_text.append(f"  Confirm Dangerous Commands: {config.ui.confirm_dangerous_commands}")
            
            config_text.append("\nProviders:")
            for name, provider_config in config.providers.items():
                status = "enabled" if provider_config.enabled else "disabled"
                config_text.append(f"  {name}: {status}")
            
            return "\n".join(config_text)
        
        action = args[0]
        
        if action == "show":
            return await self._cmd_config([])
        
        elif action == "set":
            if len(args) < 3:
                return "Usage: /config set <key> <value>"
            
            key, value = args[1], args[2]
            # This would need to be implemented in config_manager
            return f"Setting {key} = {value} (not implemented yet)"
        
        elif action == "reset":
            # Reset to defaults
            return "Config reset (not implemented yet)"
        
        else:
            return f"Unknown config action: {action}\nAvailable actions: show, set, reset"
    
    async def _cmd_status(self, args: List[str]) -> str:
        """Show system status."""
        status_text = []
        status_text.append("System Status:")
        
        # Session info
        session = session_manager.get_active_session()
        if session:
            status_text.append(f"  Active Session: {session.id}")
            status_text.append(f"  Provider: {session.provider_name}")
            status_text.append(f"  Model: {session.model}")
            status_text.append(f"  Messages: {len(session.messages)}")
        else:
            status_text.append("  No active session")
        
        # Provider status
        config = config_manager.load_config()
        status_text.append("\nProviders:")
        for name, provider_config in config.providers.items():
            status = "✅" if provider_config.enabled else "❌"
            status_text.append(f"  {status} {name}")
        
        return "\n".join(status_text)
    
    async def _cmd_debug(self, args: List[str]) -> str:
        """Toggle debug mode."""
        if not args:
            # Show current debug status
            current_level = logging.getLogger().getEffectiveLevel()
            debug_on = current_level <= logging.DEBUG
            return f"Debug mode: {'ON' if debug_on else 'OFF'}"
        
        mode = args[0].lower()
        if mode in ["on", "true", "1"]:
            logging.getLogger().setLevel(logging.DEBUG)
            return "Debug mode enabled"
        elif mode in ["off", "false", "0"]:
            logging.getLogger().setLevel(logging.INFO)
            return "Debug mode disabled"
        else:
            return "Usage: /debug [on|off]"
    
    def get_completion_suggestions(self, text: str) -> List[str]:
        """
        Get command completion suggestions.
        
        Args:
            text: Partial command text
            
        Returns:
            List of completion suggestions
        """
        if not text.startswith('/'):
            return []
        
        partial_command = text[1:]
        suggestions = []
        
        for command in self.commands.keys():
            if command.startswith(partial_command):
                suggestions.append(f"/{command}")
        
        return suggestions
