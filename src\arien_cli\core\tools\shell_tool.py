"""
Shell Tool for Arien CLI.

This module provides a comprehensive shell command execution tool with safety checks,
output processing, and intelligent execution strategies.
"""

import asyncio
import logging
import os
import platform
import shlex
import subprocess
import sys
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import psutil

logger = logging.getLogger(__name__)


class ExecutionMode(str, Enum):
    """Execution modes for shell commands."""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    INTERACTIVE = "interactive"
    BACKGROUND = "background"


class SafetyLevel(str, Enum):
    """Safety levels for command execution."""
    SAFE = "safe"           # Read-only operations, safe commands
    MODERATE = "moderate"   # File operations, package management
    DANGEROUS = "dangerous" # System modifications, destructive operations
    CRITICAL = "critical"   # Potentially system-breaking commands


@dataclass
class CommandResult:
    """Result of a shell command execution."""
    command: str
    exit_code: int
    stdout: str
    stderr: str
    execution_time: float
    working_directory: str
    environment: Dict[str, str]
    process_id: Optional[int] = None
    safety_level: Optional[SafetyLevel] = None


@dataclass
class CommandContext:
    """Context for command execution."""
    working_directory: str
    environment: Dict[str, str]
    timeout: Optional[float] = None
    shell: bool = True
    capture_output: bool = True


class ShellTool:
    """
    Advanced shell command execution tool.
    
    Features:
    - Safety classification and validation
    - Multiple execution modes (sequential, parallel, interactive)
    - Output streaming and processing
    - Environment management
    - Cross-platform compatibility
    - Process monitoring and control
    
    Usage Guidelines:
    
    WHEN TO USE:
    - File system operations (ls, find, grep, etc.)
    - Package management (pip, npm, apt, etc.)
    - Development tasks (git, build tools, etc.)
    - System information gathering
    - Text processing and data manipulation
    
    WHEN TO USE SEQUENTIALLY:
    - Commands that depend on previous command results
    - File operations that must complete before next step
    - Installation/setup procedures
    - Database migrations or schema changes
    
    WHEN TO USE IN PARALLEL:
    - Independent file processing tasks
    - Multiple API calls or downloads
    - Batch operations on different directories
    - Parallel testing or validation
    
    WHEN NOT TO USE:
    - Commands requiring user interaction (use interactive mode)
    - Long-running services (use background mode)
    - Commands that might damage the system
    - Operations requiring elevated privileges without explicit approval
    
    SAFETY CONSIDERATIONS:
    - Commands are classified by safety level
    - Dangerous operations require explicit confirmation
    - File system boundaries are enforced
    - Resource usage is monitored
    """
    
    # Dangerous command patterns
    DANGEROUS_PATTERNS = [
        r'rm\s+-rf\s+/',
        r'sudo\s+rm',
        r'dd\s+if=',
        r'mkfs\.',
        r'fdisk',
        r'parted',
        r'format\s+c:',
        r'del\s+/s\s+/q',
        r'shutdown',
        r'reboot',
        r'halt',
        r'init\s+0',
        r'kill\s+-9\s+1',
        r'chmod\s+777\s+/',
        r'chown\s+.*\s+/',
    ]
    
    # Safe command prefixes
    SAFE_COMMANDS = {
        'ls', 'dir', 'cat', 'type', 'head', 'tail', 'grep', 'find', 'locate',
        'pwd', 'whoami', 'id', 'date', 'uptime', 'ps', 'top', 'htop',
        'df', 'du', 'free', 'lscpu', 'lsblk', 'lsusb', 'lspci',
        'echo', 'printf', 'wc', 'sort', 'uniq', 'cut', 'awk', 'sed',
        'git status', 'git log', 'git diff', 'git show',
        'python --version', 'node --version', 'npm list',
        'pip list', 'pip show', 'conda list'
    }
    
    def __init__(self, working_directory: Optional[str] = None):
        """
        Initialize the shell tool.
        
        Args:
            working_directory: Default working directory for commands
        """
        self.working_directory = working_directory or os.getcwd()
        self.environment = os.environ.copy()
        self.active_processes: Dict[int, subprocess.Popen] = {}
        
        # Platform-specific settings
        self.is_windows = platform.system() == "Windows"
        self.shell_executable = self._get_shell_executable()
    
    def _get_shell_executable(self) -> str:
        """Get the appropriate shell executable for the platform."""
        if self.is_windows:
            return os.environ.get('COMSPEC', 'cmd.exe')
        else:
            return os.environ.get('SHELL', '/bin/bash')
    
    def classify_safety_level(self, command: str) -> SafetyLevel:
        """
        Classify the safety level of a command.
        
        Args:
            command: The command to classify
            
        Returns:
            SafetyLevel enum value
        """
        import re
        
        command_lower = command.lower().strip()
        
        # Check for dangerous patterns
        for pattern in self.DANGEROUS_PATTERNS:
            if re.search(pattern, command_lower):
                return SafetyLevel.CRITICAL
        
        # Check for potentially dangerous operations
        dangerous_keywords = [
            'sudo', 'su', 'chmod 777', 'chown', 'rm -r', 'del /s',
            'format', 'fdisk', 'parted', 'mkfs', 'dd if=',
            'iptables', 'ufw', 'firewall', 'registry', 'regedit'
        ]
        
        for keyword in dangerous_keywords:
            if keyword in command_lower:
                return SafetyLevel.DANGEROUS
        
        # Check for moderate operations
        moderate_keywords = [
            'install', 'uninstall', 'update', 'upgrade', 'remove',
            'mkdir', 'rmdir', 'touch', 'cp', 'mv', 'copy', 'move',
            'git clone', 'git pull', 'git push', 'git commit'
        ]
        
        for keyword in moderate_keywords:
            if keyword in command_lower:
                return SafetyLevel.MODERATE
        
        # Check if it's a known safe command
        command_parts = command_lower.split()
        if command_parts and command_parts[0] in self.SAFE_COMMANDS:
            return SafetyLevel.SAFE
        
        # Default to moderate for unknown commands
        return SafetyLevel.MODERATE
    
    async def execute_command(
        self,
        command: str,
        context: Optional[CommandContext] = None,
        mode: ExecutionMode = ExecutionMode.SEQUENTIAL,
        require_confirmation: bool = False
    ) -> CommandResult:
        """
        Execute a single shell command.
        
        Args:
            command: The command to execute
            context: Execution context (working dir, env, etc.)
            mode: Execution mode
            require_confirmation: Whether to require user confirmation
            
        Returns:
            CommandResult with execution details
        """
        if not context:
            context = CommandContext(
                working_directory=self.working_directory,
                environment=self.environment
            )
        
        safety_level = self.classify_safety_level(command)
        
        # Safety check
        if safety_level == SafetyLevel.CRITICAL:
            raise ValueError(f"Command classified as CRITICAL safety risk: {command}")
        
        if safety_level == SafetyLevel.DANGEROUS and require_confirmation:
            logger.warning(f"Dangerous command requires confirmation: {command}")
            # In a real implementation, this would prompt the user
            # For now, we'll log and continue
        
        logger.info(f"Executing command: {command} (Safety: {safety_level.value})")
        
        start_time = asyncio.get_event_loop().time()
        
        try:
            if mode == ExecutionMode.INTERACTIVE:
                return await self._execute_interactive(command, context)
            elif mode == ExecutionMode.BACKGROUND:
                return await self._execute_background(command, context)
            else:
                return await self._execute_standard(command, context)
        
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            logger.error(f"Command execution failed: {e}")
            
            return CommandResult(
                command=command,
                exit_code=-1,
                stdout="",
                stderr=str(e),
                execution_time=execution_time,
                working_directory=context.working_directory,
                environment=context.environment,
                safety_level=safety_level
            )
    
    async def _execute_standard(
        self, 
        command: str, 
        context: CommandContext
    ) -> CommandResult:
        """Execute command in standard mode."""
        start_time = asyncio.get_event_loop().time()
        
        # Prepare command for execution
        if context.shell:
            if self.is_windows:
                cmd_args = [self.shell_executable, '/c', command]
            else:
                cmd_args = [self.shell_executable, '-c', command]
        else:
            cmd_args = shlex.split(command)
        
        # Execute the command
        process = await asyncio.create_subprocess_exec(
            *cmd_args,
            stdout=subprocess.PIPE if context.capture_output else None,
            stderr=subprocess.PIPE if context.capture_output else None,
            cwd=context.working_directory,
            env=context.environment
        )
        
        try:
            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=context.timeout
            )
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            return CommandResult(
                command=command,
                exit_code=process.returncode or 0,
                stdout=stdout.decode('utf-8', errors='replace') if stdout else "",
                stderr=stderr.decode('utf-8', errors='replace') if stderr else "",
                execution_time=execution_time,
                working_directory=context.working_directory,
                environment=context.environment,
                process_id=process.pid
            )
        
        except asyncio.TimeoutError:
            process.kill()
            await process.wait()
            execution_time = asyncio.get_event_loop().time() - start_time
            
            return CommandResult(
                command=command,
                exit_code=-1,
                stdout="",
                stderr=f"Command timed out after {context.timeout} seconds",
                execution_time=execution_time,
                working_directory=context.working_directory,
                environment=context.environment,
                process_id=process.pid
            )
    
    async def _execute_interactive(
        self, 
        command: str, 
        context: CommandContext
    ) -> CommandResult:
        """Execute command in interactive mode."""
        # For interactive mode, we would typically use a different approach
        # This is a simplified implementation
        return await self._execute_standard(command, context)
    
    async def _execute_background(
        self, 
        command: str, 
        context: CommandContext
    ) -> CommandResult:
        """Execute command in background mode."""
        start_time = asyncio.get_event_loop().time()
        
        if self.is_windows:
            cmd_args = [self.shell_executable, '/c', command]
        else:
            cmd_args = [self.shell_executable, '-c', command]
        
        process = await asyncio.create_subprocess_exec(
            *cmd_args,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=context.working_directory,
            env=context.environment
        )
        
        # Store process for later monitoring
        self.active_processes[process.pid] = process
        
        execution_time = asyncio.get_event_loop().time() - start_time
        
        return CommandResult(
            command=command,
            exit_code=0,  # Process started successfully
            stdout=f"Background process started with PID: {process.pid}",
            stderr="",
            execution_time=execution_time,
            working_directory=context.working_directory,
            environment=context.environment,
            process_id=process.pid
        )

    async def execute_commands_parallel(
        self,
        commands: List[str],
        context: Optional[CommandContext] = None,
        max_concurrent: int = 5
    ) -> List[CommandResult]:
        """
        Execute multiple commands in parallel.

        Args:
            commands: List of commands to execute
            context: Execution context
            max_concurrent: Maximum number of concurrent executions

        Returns:
            List of CommandResult objects
        """
        if not context:
            context = CommandContext(
                working_directory=self.working_directory,
                environment=self.environment
            )

        semaphore = asyncio.Semaphore(max_concurrent)

        async def execute_with_semaphore(cmd: str) -> CommandResult:
            async with semaphore:
                return await self.execute_command(cmd, context, ExecutionMode.PARALLEL)

        tasks = [execute_with_semaphore(cmd) for cmd in commands]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Convert exceptions to error results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(CommandResult(
                    command=commands[i],
                    exit_code=-1,
                    stdout="",
                    stderr=str(result),
                    execution_time=0.0,
                    working_directory=context.working_directory,
                    environment=context.environment
                ))
            else:
                processed_results.append(result)

        return processed_results

    async def execute_commands_sequential(
        self,
        commands: List[str],
        context: Optional[CommandContext] = None,
        stop_on_error: bool = True
    ) -> List[CommandResult]:
        """
        Execute multiple commands sequentially.

        Args:
            commands: List of commands to execute
            context: Execution context
            stop_on_error: Whether to stop execution on first error

        Returns:
            List of CommandResult objects
        """
        if not context:
            context = CommandContext(
                working_directory=self.working_directory,
                environment=self.environment
            )

        results = []

        for command in commands:
            result = await self.execute_command(command, context, ExecutionMode.SEQUENTIAL)
            results.append(result)

            if stop_on_error and result.exit_code != 0:
                logger.warning(f"Command failed, stopping execution: {command}")
                break

        return results

    def get_process_status(self, pid: int) -> Optional[Dict[str, Any]]:
        """
        Get status of a background process.

        Args:
            pid: Process ID

        Returns:
            Process status information or None if not found
        """
        try:
            process = psutil.Process(pid)
            return {
                "pid": pid,
                "status": process.status(),
                "cpu_percent": process.cpu_percent(),
                "memory_info": process.memory_info()._asdict(),
                "create_time": process.create_time(),
                "cmdline": process.cmdline()
            }
        except psutil.NoSuchProcess:
            return None

    async def kill_process(self, pid: int, force: bool = False) -> bool:
        """
        Kill a background process.

        Args:
            pid: Process ID to kill
            force: Whether to force kill (SIGKILL vs SIGTERM)

        Returns:
            True if process was killed, False otherwise
        """
        try:
            process = psutil.Process(pid)

            if force:
                process.kill()
            else:
                process.terminate()

            # Remove from active processes
            if pid in self.active_processes:
                del self.active_processes[pid]

            return True

        except psutil.NoSuchProcess:
            return False
        except psutil.AccessDenied:
            logger.error(f"Access denied when trying to kill process {pid}")
            return False

    def get_tool_definition(self) -> Dict[str, Any]:
        """
        Get the tool definition for LLM function calling.

        Returns:
            Tool definition in OpenAI function calling format
        """
        return {
            "type": "function",
            "function": {
                "name": "execute_shell_command",
                "description": """
Execute shell commands with comprehensive safety checks and output processing.

USAGE GUIDELINES:

WHEN TO USE:
- File system operations (ls, find, grep, cat, etc.)
- Package management (pip install, npm install, apt update, etc.)
- Development tasks (git operations, build commands, etc.)
- System information gathering (ps, df, free, etc.)
- Text processing and data manipulation

WHEN TO USE SEQUENTIALLY:
- Commands that depend on previous results
- Installation procedures that must complete in order
- File operations that must finish before next step
- Database migrations or schema changes

WHEN TO USE IN PARALLEL:
- Independent file processing tasks
- Multiple downloads or API calls
- Batch operations on different directories
- Parallel testing or validation

WHEN NOT TO USE:
- Commands requiring user interaction
- Long-running services (use background mode)
- Potentially destructive operations without confirmation
- Commands requiring elevated privileges

SAFETY LEVELS:
- SAFE: Read-only operations, information gathering
- MODERATE: File operations, package management
- DANGEROUS: System modifications, user management
- CRITICAL: Potentially system-breaking operations (blocked)

OUTPUT PROCESSING:
- Captures both stdout and stderr
- Provides execution time and exit codes
- Monitors resource usage for background processes
- Formats output for easy parsing and display
                """,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "command": {
                            "type": "string",
                            "description": "The shell command to execute"
                        },
                        "working_directory": {
                            "type": "string",
                            "description": "Working directory for command execution (optional)"
                        },
                        "mode": {
                            "type": "string",
                            "enum": ["sequential", "parallel", "interactive", "background"],
                            "description": "Execution mode for the command"
                        },
                        "timeout": {
                            "type": "number",
                            "description": "Timeout in seconds (optional)"
                        },
                        "environment": {
                            "type": "object",
                            "description": "Additional environment variables (optional)"
                        }
                    },
                    "required": ["command"]
                }
            }
        }
