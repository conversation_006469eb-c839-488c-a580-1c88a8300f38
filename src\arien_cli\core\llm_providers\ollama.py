"""
Ollama LLM Provider for Arien CLI.

This module implements the Ollama provider for local LLM inference.
Supports all models available in the local Ollama installation.
"""

import json
import logging
from typing import Any, AsyncGenerator, Dict, List, Optional, Union

import httpx
from pydantic import ValidationError

from .base import (
    ChatMessage, 
    LLMProvider, 
    LLMResponse, 
    MessageRole, 
    StreamChunk, 
    ToolCall
)

logger = logging.getLogger(__name__)


class OllamaProvider(LLMProvider):
    """
    Ollama LLM Provider implementation.
    
    Supports:
    - All locally installed Ollama models
    - Function calling (where supported by model)
    - Streaming responses
    - Model management
    
    Features:
    - Local inference (no API key required)
    - Dynamic model discovery
    - Robust error handling
    - Custom model parameters
    """
    
    DEFAULT_BASE_URL = "http://localhost:11434"
    
    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        """
        Initialize Ollama provider.
        
        Args:
            api_key: Not required for Ollama (local)
            base_url: Ollama server URL (defaults to localhost:11434)
        """
        super().__init__(api_key, base_url or self.DEFAULT_BASE_URL)
        self._client: Optional[httpx.AsyncClient] = None
        self._cached_models: Optional[List[str]] = None
    
    @property
    def name(self) -> str:
        """Return provider name."""
        return "Ollama"
    
    @property
    def available_models(self) -> List[str]:
        """Return cached available models or empty list if not fetched."""
        return self._cached_models or []
    
    async def initialize(self) -> None:
        """Initialize the Ollama client and fetch available models."""
        self._client = httpx.AsyncClient(
            base_url=self.base_url,
            headers={"Content-Type": "application/json"},
            timeout=httpx.Timeout(120.0)  # Longer timeout for local inference
        )
        
        # Fetch available models
        await self._fetch_models()
        
        logger.info(f"Initialized Ollama provider with {len(self.available_models)} models")
    
    async def _fetch_models(self) -> None:
        """Fetch available models from Ollama."""
        try:
            if not self._client:
                await self.initialize()
            
            response = await self._client.get("/api/tags")
            response.raise_for_status()
            
            data = response.json()
            self._cached_models = [model["name"] for model in data.get("models", [])]
            
        except Exception as e:
            logger.error(f"Failed to fetch Ollama models: {e}")
            self._cached_models = []
    
    async def validate_connection(self) -> bool:
        """Validate connection to Ollama server."""
        try:
            if not self._client:
                await self.initialize()
            
            response = await self._client.get("/api/tags")
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Ollama connection validation failed: {e}")
            return False
    
    def _convert_messages(self, messages: List[ChatMessage]) -> List[Dict[str, Any]]:
        """Convert ChatMessage objects to Ollama API format."""
        converted = []
        for msg in messages:
            message_dict = {
                "role": msg.role.value,
                "content": msg.content
            }
            
            # Ollama handles tool calls differently
            if msg.tool_calls:
                # Convert tool calls to a format Ollama can understand
                tool_content = f"Tool calls: {json.dumps(msg.tool_calls, indent=2)}"
                message_dict["content"] = f"{msg.content}\n\n{tool_content}"
            
            converted.append(message_dict)
        
        return converted
    
    async def chat_completion(
        self,
        messages: List[ChatMessage],
        model: str,
        tools: Optional[List[Dict[str, Any]]] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[LLMResponse, AsyncGenerator[StreamChunk, None]]:
        """Generate chat completion using Ollama API."""
        if not self._client:
            await self.initialize()
        
        # Refresh models if the requested model is not in cache
        if model not in self.available_models:
            await self._fetch_models()
            if model not in self.available_models:
                raise ValueError(f"Model {model} not available. Available models: {self.available_models}")
        
        # Prepare request payload
        payload = {
            "model": model,
            "messages": self._convert_messages(messages),
            "stream": stream,
            "options": {
                "temperature": temperature,
            }
        }
        
        if max_tokens:
            payload["options"]["num_predict"] = max_tokens
        
        # Add tools as system message if provided
        if tools:
            tools_description = self._format_tools_for_ollama(tools)
            system_msg = {
                "role": "system",
                "content": f"You have access to the following tools:\n\n{tools_description}\n\nWhen you need to use a tool, respond with a JSON object containing 'tool_name' and 'arguments' fields."
            }
            payload["messages"].insert(0, system_msg)
        
        # Add any additional options
        if kwargs:
            payload["options"].update(kwargs)
        
        try:
            if stream:
                return self._stream_completion(payload)
            else:
                return await self._non_stream_completion(payload)
        
        except httpx.HTTPStatusError as e:
            logger.error(f"Ollama API error: {e.response.status_code} - {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in Ollama completion: {e}")
            raise
    
    def _format_tools_for_ollama(self, tools: List[Dict[str, Any]]) -> str:
        """Format tools description for Ollama models."""
        formatted_tools = []
        for tool in tools:
            func = tool.get("function", {})
            name = func.get("name", "unknown")
            description = func.get("description", "No description")
            parameters = func.get("parameters", {})
            
            tool_desc = f"**{name}**: {description}"
            if parameters.get("properties"):
                props = parameters["properties"]
                params_desc = ", ".join([f"{k}: {v.get('description', 'No description')}" for k, v in props.items()])
                tool_desc += f"\nParameters: {params_desc}"
            
            formatted_tools.append(tool_desc)
        
        return "\n\n".join(formatted_tools)
    
    async def _non_stream_completion(self, payload: Dict[str, Any]) -> LLMResponse:
        """Handle non-streaming completion."""
        response = await self._client.post("/api/chat", json=payload)
        response.raise_for_status()
        
        data = response.json()
        message = data.get("message", {})
        content = message.get("content", "")
        
        # Try to parse tool calls from response
        tool_calls = self._extract_tool_calls(content)
        
        return LLMResponse(
            content=content,
            tool_calls=tool_calls,
            usage=None,  # Ollama doesn't provide usage stats
            finish_reason=data.get("done_reason"),
            model=data.get("model")
        )
    
    async def _stream_completion(self, payload: Dict[str, Any]) -> AsyncGenerator[StreamChunk, None]:
        """Handle streaming completion."""
        async with self._client.stream("POST", "/api/chat", json=payload) as response:
            response.raise_for_status()
            
            accumulated_content = ""
            
            async for line in response.aiter_lines():
                if line.strip():
                    try:
                        data = json.loads(line)
                        message = data.get("message", {})
                        content = message.get("content", "")
                        
                        if content:
                            accumulated_content += content
                        
                        chunk = StreamChunk(
                            content=content,
                            finish_reason=data.get("done_reason") if data.get("done") else None
                        )
                        
                        yield chunk
                        
                        # If done, try to extract tool calls from full content
                        if data.get("done"):
                            tool_calls = self._extract_tool_calls(accumulated_content)
                            if tool_calls:
                                yield StreamChunk(tool_calls=tool_calls)
                        
                    except json.JSONDecodeError:
                        continue  # Skip malformed JSON
    
    def _extract_tool_calls(self, content: str) -> List[ToolCall]:
        """Extract tool calls from model response content."""
        tool_calls = []
        
        # Look for JSON objects that might be tool calls
        try:
            # Simple heuristic: look for JSON-like structures
            import re
            json_pattern = r'\{[^{}]*"tool_name"[^{}]*\}'
            matches = re.findall(json_pattern, content)
            
            for match in matches:
                try:
                    tool_data = json.loads(match)
                    if "tool_name" in tool_data:
                        tool_calls.append(ToolCall(
                            id=f"call_{len(tool_calls)}",
                            name=tool_data["tool_name"],
                            arguments=tool_data.get("arguments", {})
                        ))
                except json.JSONDecodeError:
                    continue
        
        except Exception as e:
            logger.debug(f"Failed to extract tool calls: {e}")
        
        return tool_calls
    
    async def close(self) -> None:
        """Close the HTTP client."""
        if self._client:
            await self._client.aclose()
            self._client = None
