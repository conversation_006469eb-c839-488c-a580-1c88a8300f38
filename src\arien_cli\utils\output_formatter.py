"""
Output formatting utilities for Arien CLI.

This module provides utilities for formatting and processing command outputs,
LLM responses, and other text content for display in the terminal.
"""

import json
import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from rich.console import Console
from rich.markdown import Markdown
from rich.panel import Panel
from rich.syntax import Syntax
from rich.table import Table
from rich.text import Text


class OutputFormatter:
    """Formats various types of output for terminal display."""
    
    def __init__(self, console: Optional[Console] = None):
        """
        Initialize output formatter.
        
        Args:
            console: Rich console instance
        """
        self.console = console or Console()
    
    def format_command_result(self, result: Dict[str, Any]) -> str:
        """
        Format shell command execution result.
        
        Args:
            result: Command result dictionary
            
        Returns:
            Formatted string
        """
        lines = []
        
        # Command header
        lines.append(f"Command: {result.get('command', 'Unknown')}")
        lines.append(f"Exit Code: {result.get('exit_code', 'Unknown')}")
        lines.append(f"Execution Time: {result.get('execution_time', 0):.3f}s")
        
        if result.get('working_directory'):
            lines.append(f"Working Directory: {result['working_directory']}")
        
        if result.get('safety_level'):
            lines.append(f"Safety Level: {result['safety_level'].upper()}")
        
        lines.append("")  # Empty line
        
        # Output sections
        stdout = result.get('stdout', '').strip()
        stderr = result.get('stderr', '').strip()
        
        if stdout:
            lines.append("STDOUT:")
            lines.append(stdout)
            lines.append("")
        
        if stderr:
            lines.append("STDERR:")
            lines.append(stderr)
            lines.append("")
        
        return "\n".join(lines)
    
    def format_tool_result(self, tool_name: str, result: Dict[str, Any]) -> str:
        """
        Format tool execution result.
        
        Args:
            tool_name: Name of the executed tool
            result: Tool result dictionary
            
        Returns:
            Formatted string
        """
        lines = []
        
        # Tool header
        lines.append(f"Tool: {tool_name}")
        lines.append(f"Success: {result.get('success', False)}")
        
        if 'error' in result:
            lines.append(f"Error: {result['error']}")
        
        lines.append("")
        
        # Format specific tool results
        if tool_name == "execute_shell_command":
            return self.format_command_result(result)
        
        # Generic result formatting
        for key, value in result.items():
            if key not in ['success', 'error']:
                if isinstance(value, (dict, list)):
                    lines.append(f"{key.title()}:")
                    lines.append(json.dumps(value, indent=2))
                else:
                    lines.append(f"{key.title()}: {value}")
        
        return "\n".join(lines)
    
    def format_error(self, error: str, suggestions: Optional[List[str]] = None) -> str:
        """
        Format error message with optional recovery suggestions.
        
        Args:
            error: Error message
            suggestions: Recovery suggestions
            
        Returns:
            Formatted error string
        """
        lines = [f"Error: {error}"]
        
        if suggestions:
            lines.append("")
            lines.append("Suggestions:")
            for suggestion in suggestions:
                lines.append(f"  • {suggestion}")
        
        return "\n".join(lines)
    
    def format_thinking(self, content: str) -> str:
        """
        Format thinking/reasoning content.
        
        Args:
            content: Thinking content
            
        Returns:
            Formatted string
        """
        return f"🤔 Thinking: {content}"
    
    def format_code_block(self, code: str, language: str = "bash") -> str:
        """
        Format code block with syntax highlighting.
        
        Args:
            code: Code content
            language: Programming language
            
        Returns:
            Formatted code string
        """
        return f"```{language}\n{code}\n```"
    
    def extract_code_blocks(self, text: str) -> List[Dict[str, str]]:
        """
        Extract code blocks from markdown text.
        
        Args:
            text: Text containing code blocks
            
        Returns:
            List of code blocks with language and content
        """
        pattern = r'```(\w+)?\n(.*?)\n```'
        matches = re.findall(pattern, text, re.DOTALL)
        
        code_blocks = []
        for language, code in matches:
            code_blocks.append({
                'language': language or 'text',
                'code': code.strip()
            })
        
        return code_blocks
    
    def format_session_info(self, session_info: Dict[str, Any]) -> str:
        """
        Format session information.
        
        Args:
            session_info: Session information dictionary
            
        Returns:
            Formatted string
        """
        lines = [
            f"Session ID: {session_info.get('id', 'Unknown')}",
            f"Name: {session_info.get('name', 'Unnamed')}",
            f"Provider: {session_info.get('provider', 'Unknown')}",
            f"Model: {session_info.get('model', 'Unknown')}",
            f"Messages: {session_info.get('message_count', 0)}",
            f"Working Directory: {session_info.get('working_directory', 'Unknown')}",
        ]
        
        if 'created_at' in session_info:
            created_at = session_info['created_at']
            if isinstance(created_at, datetime):
                lines.append(f"Created: {created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                lines.append(f"Created: {created_at}")
        
        if 'last_active' in session_info:
            last_active = session_info['last_active']
            if isinstance(last_active, datetime):
                lines.append(f"Last Active: {last_active.strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                lines.append(f"Last Active: {last_active}")
        
        return "\n".join(lines)
    
    def format_message_history(self, messages: List[Dict[str, Any]], limit: int = 10) -> str:
        """
        Format message history.
        
        Args:
            messages: List of message dictionaries
            limit: Maximum number of messages to show
            
        Returns:
            Formatted string
        """
        if not messages:
            return "No messages in history."
        
        lines = [f"Message History (last {min(limit, len(messages))} messages):"]
        lines.append("=" * 50)
        
        for msg in messages[-limit:]:
            timestamp = msg.get('timestamp', 'Unknown')
            if isinstance(timestamp, datetime):
                timestamp = timestamp.strftime('%H:%M:%S')
            
            role = msg.get('role', 'unknown').upper()
            content = msg.get('content', '')[:100]  # Truncate long content
            
            if len(msg.get('content', '')) > 100:
                content += "..."
            
            lines.append(f"[{timestamp}] {role}: {content}")
        
        return "\n".join(lines)
    
    def format_provider_status(self, providers: Dict[str, Any]) -> str:
        """
        Format provider status information.
        
        Args:
            providers: Provider status dictionary
            
        Returns:
            Formatted string
        """
        lines = ["Provider Status:"]
        lines.append("=" * 30)
        
        for name, info in providers.items():
            status = "✅ Available" if info.get('available', False) else "❌ Unavailable"
            lines.append(f"{name.title()}: {status}")
            
            if info.get('models'):
                models = ", ".join(info['models'][:3])  # Show first 3 models
                if len(info['models']) > 3:
                    models += f" (+{len(info['models']) - 3} more)"
                lines.append(f"  Models: {models}")
            
            if info.get('error'):
                lines.append(f"  Error: {info['error']}")
        
        return "\n".join(lines)
    
    def truncate_text(self, text: str, max_length: int = 100) -> str:
        """
        Truncate text to maximum length.
        
        Args:
            text: Text to truncate
            max_length: Maximum length
            
        Returns:
            Truncated text
        """
        if len(text) <= max_length:
            return text
        
        return text[:max_length - 3] + "..."
    
    def format_json(self, data: Any, indent: int = 2) -> str:
        """
        Format data as JSON.

        Args:
            data: Data to format
            indent: JSON indentation

        Returns:
            Formatted JSON string
        """
        try:
            return json.dumps(data, indent=indent, ensure_ascii=False, default=str)
        except (TypeError, ValueError):
            return str(data)

    def format_streaming_content(self, content: str, is_complete: bool = False) -> str:
        """
        Format streaming content with indicators.

        Args:
            content: Streaming content
            is_complete: Whether the stream is complete

        Returns:
            Formatted streaming content
        """
        if not content:
            return ""

        # Add streaming indicator if not complete
        if not is_complete:
            content += " ▋"  # Cursor indicator

        return content

    def format_tool_call_preview(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """
        Format tool call preview.

        Args:
            tool_name: Name of the tool
            arguments: Tool arguments

        Returns:
            Formatted tool call preview
        """
        lines = [
            f"🔧 Executing Tool: {tool_name}",
            "Arguments:"
        ]

        for key, value in arguments.items():
            if isinstance(value, (dict, list)):
                lines.append(f"  {key}: {self.format_json(value)}")
            else:
                lines.append(f"  {key}: {value}")

        return "\n".join(lines)

    def format_execution_progress(self, tool_name: str, status: str, elapsed_time: float = 0) -> str:
        """
        Format execution progress.

        Args:
            tool_name: Name of the executing tool
            status: Current status
            elapsed_time: Elapsed execution time

        Returns:
            Formatted progress string
        """
        status_indicators = {
            "starting": "🔄",
            "running": "⚡",
            "completed": "✅",
            "failed": "❌",
            "timeout": "⏰"
        }

        indicator = status_indicators.get(status, "🔄")
        time_str = f" ({elapsed_time:.1f}s)" if elapsed_time > 0 else ""

        return f"{indicator} {tool_name}: {status.title()}{time_str}"

    def format_error_with_context(self, error: str, context: Dict[str, Any], suggestions: Optional[List[str]] = None) -> str:
        """
        Format error with additional context.

        Args:
            error: Error message
            context: Error context information
            suggestions: Recovery suggestions

        Returns:
            Formatted error with context
        """
        lines = [
            f"❌ Error: {error}",
            ""
        ]

        if context:
            lines.append("Context:")
            for key, value in context.items():
                lines.append(f"  {key}: {value}")
            lines.append("")

        if suggestions:
            lines.append("💡 Suggestions:")
            for i, suggestion in enumerate(suggestions, 1):
                lines.append(f"  {i}. {suggestion}")

        return "\n".join(lines)

    def format_table_data(self, data: List[Dict[str, Any]], headers: Optional[List[str]] = None) -> str:
        """
        Format data as a simple text table.

        Args:
            data: List of dictionaries
            headers: Optional custom headers

        Returns:
            Formatted table string
        """
        if not data:
            return "No data to display"

        # Get headers
        if headers:
            all_headers = headers
        else:
            all_headers = list(set().union(*(d.keys() for d in data)))

        # Calculate column widths
        col_widths = {}
        for header in all_headers:
            col_widths[header] = max(
                len(str(header)),
                max(len(str(row.get(header, ""))) for row in data)
            )

        # Format table
        lines = []

        # Header row
        header_row = " | ".join(header.ljust(col_widths[header]) for header in all_headers)
        lines.append(header_row)
        lines.append("-" * len(header_row))

        # Data rows
        for row in data:
            data_row = " | ".join(str(row.get(header, "")).ljust(col_widths[header]) for header in all_headers)
            lines.append(data_row)

        return "\n".join(lines)

    def detect_content_type(self, content: str) -> str:
        """
        Detect content type for appropriate formatting.

        Args:
            content: Content to analyze

        Returns:
            Detected content type
        """
        content_lower = content.lower().strip()

        # JSON detection
        if (content_lower.startswith('{') and content_lower.endswith('}')) or \
           (content_lower.startswith('[') and content_lower.endswith(']')):
            try:
                json.loads(content)
                return "json"
            except:
                pass

        # XML detection
        if content_lower.startswith('<') and '>' in content:
            return "xml"

        # Code block detection
        if '```' in content:
            return "markdown"

        # Table-like data detection
        if '\t' in content or ('|' in content and '\n' in content):
            return "table"

        # Log-like detection
        if re.search(r'\d{4}-\d{2}-\d{2}|\d{2}:\d{2}:\d{2}|ERROR|INFO|DEBUG|WARN', content):
            return "log"

        return "text"


# Global formatter instance
output_formatter = OutputFormatter()
