"""
Deepseek LLM Provider for Arien CLI.

This module implements the Deepseek provider with support for both
deepseek-chat and deepseek-reasoner models.
"""

import json
import logging
from typing import Any, AsyncGenerator, Dict, List, Optional, Union

import httpx
from pydantic import ValidationError

from .base import (
    ChatMessage, 
    LLMProvider, 
    LLMResponse, 
    MessageRole, 
    StreamChunk, 
    ToolCall
)

logger = logging.getLogger(__name__)


class DeepseekProvider(LLMProvider):
    """
    Deepseek LLM Provider implementation.
    
    Supports:
    - deepseek-chat: Fast, efficient chat model
    - deepseek-reasoner: Advanced reasoning model
    
    Features:
    - Function calling support
    - Streaming responses
    - Robust error handling
    - Rate limit management
    """
    
    DEFAULT_BASE_URL = "https://api.deepseek.com/v1"
    
    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        """
        Initialize Deepseek provider.
        
        Args:
            api_key: Deepseek API key
            base_url: Custom base URL (defaults to official Deepseek API)
        """
        super().__init__(api_key, base_url or self.DEFAULT_BASE_URL)
        self._client: Optional[httpx.AsyncClient] = None
    
    @property
    def name(self) -> str:
        """Return provider name."""
        return "Deepseek"
    
    @property
    def available_models(self) -> List[str]:
        """Return available Deepseek models."""
        return [
            "deepseek-chat",
            "deepseek-reasoner"
        ]
    
    async def initialize(self) -> None:
        """Initialize the Deepseek client."""
        if not self.api_key:
            raise ValueError("Deepseek API key is required")
        
        self._client = httpx.AsyncClient(
            base_url=self.base_url,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            },
            timeout=httpx.Timeout(60.0)
        )
        
        logger.info(f"Initialized Deepseek provider with base URL: {self.base_url}")
    
    async def validate_connection(self) -> bool:
        """Validate connection to Deepseek API."""
        try:
            if not self._client:
                await self.initialize()
            
            # Test with a simple completion
            response = await self._client.post(
                "/chat/completions",
                json={
                    "model": "deepseek-chat",
                    "messages": [{"role": "user", "content": "Hello"}],
                    "max_tokens": 1
                }
            )
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Deepseek connection validation failed: {e}")
            return False
    
    def _convert_messages(self, messages: List[ChatMessage]) -> List[Dict[str, Any]]:
        """Convert ChatMessage objects to Deepseek API format."""
        converted = []
        for msg in messages:
            message_dict = {
                "role": msg.role.value,
                "content": msg.content
            }
            
            if msg.tool_calls:
                message_dict["tool_calls"] = msg.tool_calls
            
            if msg.tool_call_id:
                message_dict["tool_call_id"] = msg.tool_call_id
            
            if msg.name:
                message_dict["name"] = msg.name
            
            converted.append(message_dict)
        
        return converted
    
    async def chat_completion(
        self,
        messages: List[ChatMessage],
        model: str,
        tools: Optional[List[Dict[str, Any]]] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[LLMResponse, AsyncGenerator[StreamChunk, None]]:
        """Generate chat completion using Deepseek API."""
        if not self._client:
            await self.initialize()
        
        if model not in self.available_models:
            raise ValueError(f"Model {model} not available. Choose from: {self.available_models}")
        
        # Prepare request payload
        payload = {
            "model": model,
            "messages": self._convert_messages(messages),
            "temperature": temperature,
            "stream": stream
        }
        
        if max_tokens:
            payload["max_tokens"] = max_tokens
        
        if tools:
            payload["tools"] = tools
            payload["tool_choice"] = "auto"
        
        # Add any additional kwargs
        payload.update(kwargs)
        
        try:
            if stream:
                return self._stream_completion(payload)
            else:
                return await self._non_stream_completion(payload)
        
        except httpx.HTTPStatusError as e:
            logger.error(f"Deepseek API error: {e.response.status_code} - {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in Deepseek completion: {e}")
            raise
    
    async def _non_stream_completion(self, payload: Dict[str, Any]) -> LLMResponse:
        """Handle non-streaming completion."""
        response = await self._client.post("/chat/completions", json=payload)
        response.raise_for_status()
        
        data = response.json()
        choice = data["choices"][0]
        message = choice["message"]
        
        # Extract tool calls if present
        tool_calls = []
        if "tool_calls" in message and message["tool_calls"]:
            for tc in message["tool_calls"]:
                tool_calls.append(ToolCall(
                    id=tc["id"],
                    name=tc["function"]["name"],
                    arguments=json.loads(tc["function"]["arguments"])
                ))
        
        return LLMResponse(
            content=message.get("content", ""),
            tool_calls=tool_calls,
            usage=data.get("usage"),
            finish_reason=choice.get("finish_reason"),
            model=data.get("model")
        )
    
    async def _stream_completion(self, payload: Dict[str, Any]) -> AsyncGenerator[StreamChunk, None]:
        """Handle streaming completion."""
        async with self._client.stream("POST", "/chat/completions", json=payload) as response:
            response.raise_for_status()
            
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    data_str = line[6:]  # Remove "data: " prefix
                    
                    if data_str.strip() == "[DONE]":
                        break
                    
                    try:
                        data = json.loads(data_str)
                        choice = data["choices"][0]
                        delta = choice.get("delta", {})
                        
                        chunk = StreamChunk(
                            content=delta.get("content"),
                            finish_reason=choice.get("finish_reason"),
                            usage=data.get("usage")
                        )
                        
                        # Handle tool calls in streaming
                        if "tool_calls" in delta:
                            tool_calls = []
                            for tc in delta["tool_calls"]:
                                if "function" in tc:
                                    tool_calls.append(ToolCall(
                                        id=tc.get("id", ""),
                                        name=tc["function"].get("name", ""),
                                        arguments=json.loads(tc["function"].get("arguments", "{}"))
                                    ))
                            chunk.tool_calls = tool_calls
                        
                        yield chunk
                        
                    except json.JSONDecodeError:
                        continue  # Skip malformed JSON
    
    async def close(self) -> None:
        """Close the HTTP client."""
        if self._client:
            await self._client.aclose()
            self._client = None
