"""
Configuration management for Arien CLI.

This module handles configuration loading, validation, and management
for the CLI application.
"""

import json
import os
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field, validator


class LLMProviderConfig(BaseModel):
    """Configuration for an LLM provider."""
    name: str
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    default_model: Optional[str] = None
    models: list[str] = Field(default_factory=list)
    enabled: bool = True
    
    @validator('name')
    def validate_name(cls, v):
        if v not in ['deepseek', 'ollama']:
            raise ValueError(f"Unsupported provider: {v}")
        return v


class UIConfig(BaseModel):
    """Configuration for the UI."""
    theme: str = "dark"
    show_thinking: bool = True
    auto_scroll: bool = True
    max_history: int = 1000
    stream_output: bool = True
    confirm_dangerous_commands: bool = True


class SessionConfig(BaseModel):
    """Configuration for a session."""
    provider: str
    model: str
    temperature: float = 0.7
    max_tokens: Optional[int] = None
    system_prompt: Optional[str] = None
    working_directory: str = Field(default_factory=os.getcwd)


class ArienConfig(BaseModel):
    """Main configuration for Arien CLI."""
    providers: Dict[str, LLMProviderConfig] = Field(default_factory=dict)
    ui: UIConfig = Field(default_factory=UIConfig)
    default_session: SessionConfig = Field(default_factory=SessionConfig)
    log_level: str = "INFO"
    config_version: str = "1.0.0"
    
    class Config:
        extra = "allow"


class ConfigManager:
    """Manages configuration loading, saving, and validation."""
    
    def __init__(self, config_dir: Optional[Path] = None):
        """
        Initialize configuration manager.
        
        Args:
            config_dir: Custom configuration directory
        """
        self.config_dir = config_dir or self._get_default_config_dir()
        self.config_file = self.config_dir / "config.json"
        self.config: Optional[ArienConfig] = None
        
        # Ensure config directory exists
        self.config_dir.mkdir(parents=True, exist_ok=True)
    
    def _get_default_config_dir(self) -> Path:
        """Get the default configuration directory."""
        if os.name == 'nt':  # Windows
            config_dir = Path(os.environ.get('APPDATA', '')) / "arien-cli"
        else:  # Unix-like
            config_dir = Path.home() / ".config" / "arien-cli"
        
        return config_dir
    
    def load_config(self) -> ArienConfig:
        """
        Load configuration from file.
        
        Returns:
            ArienConfig instance
        """
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                self.config = ArienConfig(**config_data)
                return self.config
            
            except Exception as e:
                print(f"Error loading config: {e}")
                print("Using default configuration")
        
        # Create default configuration
        self.config = self._create_default_config()
        self.save_config()
        return self.config
    
    def _create_default_config(self) -> ArienConfig:
        """Create default configuration."""
        return ArienConfig(
            providers={
                "deepseek": LLMProviderConfig(
                    name="deepseek",
                    models=["deepseek-chat", "deepseek-reasoner"],
                    default_model="deepseek-chat"
                ),
                "ollama": LLMProviderConfig(
                    name="ollama",
                    base_url="http://localhost:11434",
                    models=[],  # Will be populated dynamically
                    default_model=None
                )
            },
            default_session=SessionConfig(
                provider="deepseek",
                model="deepseek-chat",
                system_prompt=self._get_default_system_prompt()
            )
        )
    
    def save_config(self) -> None:
        """Save configuration to file."""
        if not self.config:
            return
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config.dict(), f, indent=2, ensure_ascii=False)
        
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def update_provider_config(
        self, 
        provider_name: str, 
        **kwargs
    ) -> None:
        """
        Update provider configuration.
        
        Args:
            provider_name: Name of the provider
            **kwargs: Configuration updates
        """
        if not self.config:
            self.load_config()
        
        if provider_name not in self.config.providers:
            self.config.providers[provider_name] = LLMProviderConfig(name=provider_name)
        
        provider_config = self.config.providers[provider_name]
        for key, value in kwargs.items():
            if hasattr(provider_config, key):
                setattr(provider_config, key, value)
        
        self.save_config()
    
    def get_provider_config(self, provider_name: str) -> Optional[LLMProviderConfig]:
        """
        Get configuration for a specific provider.
        
        Args:
            provider_name: Name of the provider
            
        Returns:
            LLMProviderConfig or None if not found
        """
        if not self.config:
            self.load_config()
        
        return self.config.providers.get(provider_name)
    
    def _get_default_system_prompt(self) -> str:
        """Get the default system prompt."""
        return """You are Arien, an advanced AI assistant with access to shell command execution capabilities.

## Core Capabilities

You can execute shell commands to help users with:
- File system operations and navigation
- Package management and software installation
- Development tasks (git, build tools, testing)
- System administration and monitoring
- Data processing and analysis
- Text manipulation and search operations

## Important Guidelines

### Command Execution Strategy
1. **Analyze First**: Always understand what the user wants to achieve before executing commands
2. **Safety First**: Classify commands by safety level and proceed accordingly
3. **Explain Actions**: Describe what you're going to do before executing commands
4. **Verify Results**: Check command outputs and exit codes to ensure success
5. **Handle Errors**: Provide clear explanations when commands fail and suggest solutions

### When to Use Sequential vs Parallel Execution
- **Sequential**: Use when commands depend on each other or modify the same resources
- **Parallel**: Use for independent operations like processing multiple files or directories

### Safety Considerations
- Always verify dangerous operations before execution
- Use appropriate working directories to avoid unintended modifications
- Monitor resource usage for long-running operations
- Provide clear warnings for potentially destructive commands

### Error Handling and Recovery
- Check exit codes and provide meaningful error explanations
- Suggest alternative approaches when commands fail
- Use retry logic for transient failures
- Gracefully handle permission and access issues

### User Communication
- Stream command outputs in real-time when possible
- Provide progress updates for long-running operations
- Explain technical concepts in user-friendly terms
- Ask for clarification when requirements are ambiguous

## Function Calling Guidelines

When using the `execute_shell_command` function:
1. Choose appropriate execution mode (sequential/parallel/interactive/background)
2. Set reasonable timeouts for commands
3. Specify working directory when relevant
4. Handle both stdout and stderr appropriately
5. Process and format outputs for user readability

## Never Give Up Approach

- Retry failed operations with different approaches
- Break down complex tasks into smaller, manageable steps
- Use alternative tools or methods when primary approach fails
- Provide multiple solutions when possible
- Learn from failures to improve subsequent attempts

Remember: You are here to help users accomplish their goals efficiently and safely. Always prioritize user safety and data integrity while being helpful and thorough in your assistance."""


# Global configuration manager instance
config_manager = ConfigManager()
