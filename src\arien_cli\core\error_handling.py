"""
Error handling and retry logic for Arien CLI.

This module provides comprehensive error handling, retry mechanisms,
and recovery strategies for robust operation.
"""

import asyncio
import logging
import random
import time
from dataclasses import dataclass
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Type, Union

import httpx

logger = logging.getLogger(__name__)


class ErrorType(str, Enum):
    """Types of errors that can occur."""
    NETWORK_ERROR = "network_error"
    API_ERROR = "api_error"
    RATE_LIMIT = "rate_limit"
    AUTHENTICATION = "authentication"
    PERMISSION_DENIED = "permission_denied"
    COMMAND_FAILED = "command_failed"
    TIMEOUT = "timeout"
    VALIDATION_ERROR = "validation_error"
    UNKNOWN_ERROR = "unknown_error"


class ErrorSeverity(str, Enum):
    """Severity levels for errors."""
    LOW = "low"           # Minor issues, can continue
    MEDIUM = "medium"     # Significant issues, may need retry
    HIGH = "high"         # Major issues, requires intervention
    CRITICAL = "critical" # System-breaking issues


@dataclass
class ErrorInfo:
    """Information about an error."""
    error_type: ErrorType
    severity: ErrorSeverity
    message: str
    details: Dict[str, Any]
    timestamp: float
    retryable: bool = True
    recovery_suggestions: List[str] = None


class RetryStrategy:
    """Defines retry behavior for different error types."""
    
    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True
    ):
        """
        Initialize retry strategy.
        
        Args:
            max_attempts: Maximum number of retry attempts
            base_delay: Base delay between retries (seconds)
            max_delay: Maximum delay between retries (seconds)
            exponential_base: Base for exponential backoff
            jitter: Whether to add random jitter to delays
        """
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
    
    def get_delay(self, attempt: int) -> float:
        """
        Calculate delay for a given attempt.
        
        Args:
            attempt: Current attempt number (0-based)
            
        Returns:
            Delay in seconds
        """
        delay = self.base_delay * (self.exponential_base ** attempt)
        delay = min(delay, self.max_delay)
        
        if self.jitter:
            # Add ±25% jitter
            jitter_range = delay * 0.25
            delay += random.uniform(-jitter_range, jitter_range)
        
        return max(0, delay)


class ErrorClassifier:
    """Classifies errors and determines appropriate handling."""
    
    @staticmethod
    def classify_error(error: Exception) -> ErrorInfo:
        """
        Classify an error and return error information.
        
        Args:
            error: The exception to classify
            
        Returns:
            ErrorInfo with classification details
        """
        error_type = ErrorType.UNKNOWN_ERROR
        severity = ErrorSeverity.MEDIUM
        retryable = True
        recovery_suggestions = []
        
        if isinstance(error, httpx.TimeoutException):
            error_type = ErrorType.TIMEOUT
            severity = ErrorSeverity.MEDIUM
            retryable = True
            recovery_suggestions = [
                "Check network connectivity",
                "Increase timeout value",
                "Try again later"
            ]
        
        elif isinstance(error, httpx.NetworkError):
            error_type = ErrorType.NETWORK_ERROR
            severity = ErrorSeverity.HIGH
            retryable = True
            recovery_suggestions = [
                "Check internet connection",
                "Verify server URL",
                "Check firewall settings"
            ]
        
        elif isinstance(error, httpx.HTTPStatusError):
            status_code = error.response.status_code
            
            if status_code == 401:
                error_type = ErrorType.AUTHENTICATION
                severity = ErrorSeverity.HIGH
                retryable = False
                recovery_suggestions = [
                    "Check API key",
                    "Verify authentication credentials",
                    "Check account status"
                ]
            
            elif status_code == 403:
                error_type = ErrorType.PERMISSION_DENIED
                severity = ErrorSeverity.HIGH
                retryable = False
                recovery_suggestions = [
                    "Check account permissions",
                    "Verify API access rights",
                    "Contact support if needed"
                ]
            
            elif status_code == 429:
                error_type = ErrorType.RATE_LIMIT
                severity = ErrorSeverity.MEDIUM
                retryable = True
                recovery_suggestions = [
                    "Wait before retrying",
                    "Reduce request frequency",
                    "Check rate limit headers"
                ]
            
            elif 500 <= status_code < 600:
                error_type = ErrorType.API_ERROR
                severity = ErrorSeverity.HIGH
                retryable = True
                recovery_suggestions = [
                    "Server error - try again later",
                    "Check service status",
                    "Contact support if persistent"
                ]
            
            else:
                error_type = ErrorType.API_ERROR
                severity = ErrorSeverity.MEDIUM
                retryable = False
        
        elif isinstance(error, PermissionError):
            error_type = ErrorType.PERMISSION_DENIED
            severity = ErrorSeverity.HIGH
            retryable = False
            recovery_suggestions = [
                "Check file/directory permissions",
                "Run with appropriate privileges",
                "Verify access rights"
            ]
        
        elif isinstance(error, FileNotFoundError):
            error_type = ErrorType.VALIDATION_ERROR
            severity = ErrorSeverity.MEDIUM
            retryable = False
            recovery_suggestions = [
                "Check file path",
                "Verify file exists",
                "Check working directory"
            ]
        
        elif isinstance(error, ValueError):
            error_type = ErrorType.VALIDATION_ERROR
            severity = ErrorSeverity.LOW
            retryable = False
            recovery_suggestions = [
                "Check input parameters",
                "Verify data format",
                "Review command syntax"
            ]
        
        return ErrorInfo(
            error_type=error_type,
            severity=severity,
            message=str(error),
            details={"exception_type": type(error).__name__},
            timestamp=time.time(),
            retryable=retryable,
            recovery_suggestions=recovery_suggestions or []
        )


class RetryableOperation:
    """Wrapper for operations that can be retried."""
    
    def __init__(
        self,
        operation: Callable,
        retry_strategy: Optional[RetryStrategy] = None,
        error_classifier: Optional[ErrorClassifier] = None
    ):
        """
        Initialize retryable operation.
        
        Args:
            operation: The operation to retry
            retry_strategy: Retry strategy to use
            error_classifier: Error classifier to use
        """
        self.operation = operation
        self.retry_strategy = retry_strategy or RetryStrategy()
        self.error_classifier = error_classifier or ErrorClassifier()
        self.attempt_count = 0
        self.errors: List[ErrorInfo] = []
    
    async def execute(self, *args, **kwargs) -> Any:
        """
        Execute the operation with retry logic.
        
        Args:
            *args: Positional arguments for the operation
            **kwargs: Keyword arguments for the operation
            
        Returns:
            Result of the operation
            
        Raises:
            Exception: If all retry attempts fail
        """
        last_error = None
        
        for attempt in range(self.retry_strategy.max_attempts):
            self.attempt_count = attempt + 1
            
            try:
                logger.debug(f"Attempt {self.attempt_count}/{self.retry_strategy.max_attempts}")
                
                if asyncio.iscoroutinefunction(self.operation):
                    result = await self.operation(*args, **kwargs)
                else:
                    result = self.operation(*args, **kwargs)
                
                if attempt > 0:
                    logger.info(f"Operation succeeded after {self.attempt_count} attempts")
                
                return result
            
            except Exception as error:
                last_error = error
                error_info = self.error_classifier.classify_error(error)
                self.errors.append(error_info)
                
                logger.warning(
                    f"Attempt {self.attempt_count} failed: {error_info.message} "
                    f"(Type: {error_info.error_type}, Severity: {error_info.severity})"
                )
                
                # Don't retry if error is not retryable
                if not error_info.retryable:
                    logger.error(f"Error is not retryable: {error_info.message}")
                    break
                
                # Don't retry if this is the last attempt
                if attempt == self.retry_strategy.max_attempts - 1:
                    break
                
                # Calculate delay and wait
                delay = self.retry_strategy.get_delay(attempt)
                logger.info(f"Waiting {delay:.2f} seconds before retry...")
                await asyncio.sleep(delay)
        
        # All attempts failed
        logger.error(f"Operation failed after {self.attempt_count} attempts")
        
        if last_error:
            raise last_error
        else:
            raise RuntimeError("Operation failed with no specific error")


class ErrorHandler:
    """Central error handling and recovery system."""
    
    def __init__(self):
        """Initialize error handler."""
        self.error_history: List[ErrorInfo] = []
        self.retry_strategies: Dict[ErrorType, RetryStrategy] = {
            ErrorType.NETWORK_ERROR: RetryStrategy(max_attempts=3, base_delay=2.0),
            ErrorType.RATE_LIMIT: RetryStrategy(max_attempts=5, base_delay=5.0, max_delay=300.0),
            ErrorType.API_ERROR: RetryStrategy(max_attempts=3, base_delay=1.0),
            ErrorType.TIMEOUT: RetryStrategy(max_attempts=2, base_delay=1.0),
            ErrorType.COMMAND_FAILED: RetryStrategy(max_attempts=1, base_delay=0.0),
        }
    
    def get_retry_strategy(self, error_type: ErrorType) -> RetryStrategy:
        """Get retry strategy for a specific error type."""
        return self.retry_strategies.get(error_type, RetryStrategy(max_attempts=1))
    
    async def execute_with_retry(
        self,
        operation: Callable,
        *args,
        error_type: Optional[ErrorType] = None,
        **kwargs
    ) -> Any:
        """
        Execute an operation with appropriate retry strategy.
        
        Args:
            operation: Operation to execute
            *args: Positional arguments
            error_type: Expected error type for retry strategy
            **kwargs: Keyword arguments
            
        Returns:
            Operation result
        """
        if error_type:
            retry_strategy = self.get_retry_strategy(error_type)
        else:
            retry_strategy = RetryStrategy()
        
        retryable_op = RetryableOperation(operation, retry_strategy)
        
        try:
            result = await retryable_op.execute(*args, **kwargs)
            return result
        
        except Exception as error:
            # Log error to history
            error_info = ErrorClassifier.classify_error(error)
            self.error_history.append(error_info)
            
            # Re-raise the error
            raise
    
    def get_error_summary(self, limit: int = 10) -> List[ErrorInfo]:
        """Get recent error summary."""
        return self.error_history[-limit:]
    
    def get_recovery_suggestions(self, error: Exception) -> List[str]:
        """Get recovery suggestions for an error."""
        error_info = ErrorClassifier.classify_error(error)
        return error_info.recovery_suggestions


# Global error handler instance
error_handler = ErrorHandler()
