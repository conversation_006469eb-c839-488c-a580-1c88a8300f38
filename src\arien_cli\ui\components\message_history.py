"""
Message history component for Arien CLI.

This module provides comprehensive message history management and display.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from rich.console import Group, RenderableType
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.tree import Tree

from ...core.session import MessageRole, SessionMessage

logger = logging.getLogger(__name__)


class MessageHistoryComponent:
    """
    Message history management component.
    
    Provides:
    - Message filtering and search
    - History visualization
    - Export/import functionality
    - Statistics and analytics
    - Message threading and context
    """
    
    def __init__(self, max_display_messages: int = 50):
        """
        Initialize message history component.
        
        Args:
            max_display_messages: Maximum messages to display at once
        """
        self.max_display_messages = max_display_messages
        self.search_query: Optional[str] = None
        self.filter_role: Optional[MessageRole] = None
        self.filter_date_from: Optional[datetime] = None
        self.filter_date_to: Optional[datetime] = None
    
    def set_search_query(self, query: str) -> None:
        """
        Set search query for filtering messages.
        
        Args:
            query: Search query string
        """
        self.search_query = query.lower() if query else None
    
    def set_role_filter(self, role: Optional[MessageRole]) -> None:
        """
        Set role filter for messages.
        
        Args:
            role: Message role to filter by
        """
        self.filter_role = role
    
    def set_date_filter(self, date_from: Optional[datetime], date_to: Optional[datetime]) -> None:
        """
        Set date range filter for messages.
        
        Args:
            date_from: Start date for filtering
            date_to: End date for filtering
        """
        self.filter_date_from = date_from
        self.filter_date_to = date_to
    
    def clear_filters(self) -> None:
        """Clear all filters."""
        self.search_query = None
        self.filter_role = None
        self.filter_date_from = None
        self.filter_date_to = None
    
    def filter_messages(self, messages: List[SessionMessage]) -> List[SessionMessage]:
        """
        Filter messages based on current filters.
        
        Args:
            messages: List of messages to filter
            
        Returns:
            Filtered list of messages
        """
        filtered = messages
        
        # Role filter
        if self.filter_role:
            filtered = [msg for msg in filtered if msg.role == self.filter_role]
        
        # Date filter
        if self.filter_date_from:
            filtered = [msg for msg in filtered if msg.timestamp >= self.filter_date_from]
        
        if self.filter_date_to:
            filtered = [msg for msg in filtered if msg.timestamp <= self.filter_date_to]
        
        # Search filter
        if self.search_query:
            filtered = [
                msg for msg in filtered 
                if self.search_query in msg.content.lower()
            ]
        
        return filtered
    
    def get_message_statistics(self, messages: List[SessionMessage]) -> Dict[str, Any]:
        """
        Get statistics about messages.
        
        Args:
            messages: List of messages to analyze
            
        Returns:
            Dictionary with statistics
        """
        if not messages:
            return {
                "total_messages": 0,
                "by_role": {},
                "date_range": None,
                "total_characters": 0,
                "average_length": 0,
                "tool_calls": 0
            }
        
        # Count by role
        role_counts = {}
        for msg in messages:
            role = msg.role.value
            role_counts[role] = role_counts.get(role, 0) + 1
        
        # Character statistics
        total_chars = sum(len(msg.content) for msg in messages)
        avg_length = total_chars / len(messages) if messages else 0
        
        # Tool calls
        tool_calls = sum(1 for msg in messages if msg.tool_calls)
        
        # Date range
        dates = [msg.timestamp for msg in messages]
        date_range = {
            "earliest": min(dates),
            "latest": max(dates),
            "span_days": (max(dates) - min(dates)).days
        } if dates else None
        
        return {
            "total_messages": len(messages),
            "by_role": role_counts,
            "date_range": date_range,
            "total_characters": total_chars,
            "average_length": round(avg_length, 1),
            "tool_calls": tool_calls
        }
    
    def render_message_list(self, messages: List[SessionMessage]) -> RenderableType:
        """
        Render messages as a list.
        
        Args:
            messages: Messages to render
            
        Returns:
            Renderable message list
        """
        if not messages:
            return Panel(
                Text("No messages found", style="dim italic"),
                title="Message History",
                border_style="dim"
            )
        
        # Apply filters
        filtered_messages = self.filter_messages(messages)
        
        # Limit display
        display_messages = filtered_messages[-self.max_display_messages:]
        
        # Create table
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Time", style="dim", width=8)
        table.add_column("Role", style="cyan", width=10)
        table.add_column("Content", style="white", ratio=1)
        table.add_column("Tools", style="yellow", width=8)
        
        for msg in display_messages:
            time_str = msg.timestamp.strftime("%H:%M:%S")
            role_str = msg.role.value.title()
            
            # Truncate content for display
            content = msg.content
            if len(content) > 100:
                content = content[:97] + "..."
            
            # Tool calls indicator
            tools_str = "✓" if msg.tool_calls else ""
            
            table.add_row(time_str, role_str, content, tools_str)
        
        # Add filter info
        filter_info = self._get_filter_info(len(filtered_messages), len(messages))
        
        return Panel(
            Group(table, filter_info),
            title=f"Message History ({len(display_messages)}/{len(filtered_messages)})",
            border_style="blue"
        )
    
    def render_message_tree(self, messages: List[SessionMessage]) -> RenderableType:
        """
        Render messages as a conversation tree.
        
        Args:
            messages: Messages to render
            
        Returns:
            Renderable message tree
        """
        if not messages:
            return Panel(
                Text("No messages found", style="dim italic"),
                title="Conversation Tree",
                border_style="dim"
            )
        
        # Apply filters
        filtered_messages = self.filter_messages(messages)
        
        # Create tree
        tree = Tree("💬 Conversation")
        
        for i, msg in enumerate(filtered_messages[-20:]):  # Last 20 for readability
            # Create message node
            time_str = msg.timestamp.strftime("%H:%M")
            role_style = self._get_role_style(msg.role)
            
            content_preview = msg.content[:50] + "..." if len(msg.content) > 50 else msg.content
            
            message_text = Text()
            message_text.append(f"[{time_str}] ", style="dim")
            message_text.append(msg.role.value.title(), style=role_style)
            message_text.append(f": {content_preview}")
            
            message_node = tree.add(message_text)
            
            # Add tool calls as sub-nodes
            if msg.tool_calls:
                for tool_call in msg.tool_calls:
                    tool_text = Text()
                    tool_text.append("🔧 ", style="yellow")
                    tool_text.append(f"Tool: {tool_call.get('name', 'unknown')}", style="yellow bold")
                    message_node.add(tool_text)
            
            # Add tool results as sub-nodes
            if msg.tool_results:
                for result in msg.tool_results:
                    result_text = Text()
                    result_text.append("📊 ", style="green")
                    result_text.append("Result", style="green bold")
                    message_node.add(result_text)
        
        return Panel(
            tree,
            title="Conversation Tree",
            border_style="green"
        )
    
    def render_statistics(self, messages: List[SessionMessage]) -> RenderableType:
        """
        Render message statistics.
        
        Args:
            messages: Messages to analyze
            
        Returns:
            Renderable statistics panel
        """
        stats = self.get_message_statistics(messages)
        
        # Create statistics table
        stats_table = Table(show_header=False, box=None)
        stats_table.add_column("Metric", style="cyan bold", width=20)
        stats_table.add_column("Value", style="white")
        
        stats_table.add_row("Total Messages", str(stats["total_messages"]))
        stats_table.add_row("Total Characters", f"{stats['total_characters']:,}")
        stats_table.add_row("Average Length", f"{stats['average_length']} chars")
        stats_table.add_row("Tool Calls", str(stats["tool_calls"]))
        
        # Role breakdown
        if stats["by_role"]:
            stats_table.add_row("", "")  # Separator
            stats_table.add_row("By Role:", "")
            for role, count in stats["by_role"].items():
                stats_table.add_row(f"  {role.title()}", str(count))
        
        # Date range
        if stats["date_range"]:
            date_range = stats["date_range"]
            stats_table.add_row("", "")  # Separator
            stats_table.add_row("Date Range:", "")
            stats_table.add_row("  Earliest", date_range["earliest"].strftime("%Y-%m-%d %H:%M"))
            stats_table.add_row("  Latest", date_range["latest"].strftime("%Y-%m-%d %H:%M"))
            stats_table.add_row("  Span", f"{date_range['span_days']} days")
        
        return Panel(
            stats_table,
            title="Message Statistics",
            border_style="magenta"
        )
    
    def export_to_json(self, messages: List[SessionMessage], filename: str) -> None:
        """
        Export messages to JSON file.
        
        Args:
            messages: Messages to export
            filename: Output filename
        """
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "total_messages": len(messages),
            "messages": [
                {
                    "id": msg.id,
                    "role": msg.role.value,
                    "content": msg.content,
                    "timestamp": msg.timestamp.isoformat(),
                    "tool_calls": msg.tool_calls,
                    "tool_results": msg.tool_results,
                    "metadata": msg.metadata
                }
                for msg in messages
            ]
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
    
    def export_to_markdown(self, messages: List[SessionMessage], filename: str) -> None:
        """
        Export messages to Markdown file.
        
        Args:
            messages: Messages to export
            filename: Output filename
        """
        lines = [
            "# Arien CLI Chat History",
            "",
            f"Exported: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Total Messages: {len(messages)}",
            "",
            "---",
            ""
        ]
        
        for msg in messages:
            timestamp = msg.timestamp.strftime("%Y-%m-%d %H:%M:%S")
            role = msg.role.value.title()
            
            lines.append(f"## {role} - {timestamp}")
            lines.append("")
            lines.append(msg.content)
            lines.append("")
            
            if msg.tool_calls:
                lines.append("**Tool Calls:**")
                for tool_call in msg.tool_calls:
                    lines.append(f"- {tool_call.get('name', 'unknown')}")
                lines.append("")
            
            if msg.tool_results:
                lines.append("**Tool Results:**")
                for result in msg.tool_results:
                    lines.append(f"- Success: {result.get('success', 'unknown')}")
                lines.append("")
            
            lines.append("---")
            lines.append("")
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
    
    def _get_filter_info(self, filtered_count: int, total_count: int) -> Text:
        """Get filter information text."""
        filter_text = Text()
        
        if filtered_count != total_count:
            filter_text.append(f"Showing {filtered_count} of {total_count} messages", style="dim")
            
            active_filters = []
            if self.search_query:
                active_filters.append(f"search: '{self.search_query}'")
            if self.filter_role:
                active_filters.append(f"role: {self.filter_role.value}")
            if self.filter_date_from or self.filter_date_to:
                active_filters.append("date range")
            
            if active_filters:
                filter_text.append(" | Filters: ", style="dim")
                filter_text.append(", ".join(active_filters), style="yellow")
        else:
            filter_text.append(f"Showing all {total_count} messages", style="dim")
        
        return filter_text
    
    def _get_role_style(self, role: MessageRole) -> str:
        """Get style for message role."""
        role_styles = {
            MessageRole.USER: "cyan bold",
            MessageRole.ASSISTANT: "green bold",
            MessageRole.SYSTEM: "yellow bold",
            MessageRole.TOOL: "magenta bold"
        }
        return role_styles.get(role, "white")
