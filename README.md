# Arien CLI

Modern and powerful CLI terminal system with LLM integration and function calling capabilities.

## Features

### 🤖 LLM Integration
- **Deepseek Provider**: Fast and efficient chat models (deepseek-chat, deepseek-reasoner)
- **Ollama Provider**: Local LLM inference with any Ollama-compatible model
- **Function Calling**: Advanced tool execution with safety checks
- **Streaming Responses**: Real-time response streaming for better UX

### 🛠️ Shell Tool System
- **Safe Command Execution**: Multi-level safety classification (Safe, Moderate, Dangerous, Critical)
- **Multiple Execution Modes**: Sequential, Parallel, Interactive, and Background execution
- **Output Processing**: Comprehensive output capture and formatting
- **Error Handling**: Robust retry logic and error recovery
- **Resource Monitoring**: Process tracking and resource usage monitoring

### 🎨 Modern Terminal UI
- **Responsive Layout**: Adaptive terminal interface with structured components
- **Real-time Updates**: Live streaming of AI responses and command outputs
- **Rich Formatting**: Syntax highlighting, markdown rendering, and structured displays
- **Session Management**: Multiple session support with history persistence
- **Slash Commands**: Quick access to features and settings

### 🔧 Advanced Capabilities
- **Never Give Up Logic**: Intelligent retry mechanisms for failed operations
- **Cross-platform Support**: Windows 11 WSL, macOS, and Linux compatibility
- **Configuration Management**: Flexible provider and model configuration
- **Onboarding Wizard**: Guided setup for new users

## Installation

### Quick Install

```bash
# Clone the repository
git clone https://github.com/arien-ai/arien-cli.git
cd arien-cli

# Run the universal installer
python install.py install
```

### Manual Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Install in development mode
pip install -e .
```

### Global Installation Options

The installer supports multiple platforms and operations:

```bash
# Install globally
python install.py install

# Update existing installation
python install.py update

# Uninstall
python install.py uninstall

# Check installation status
python install.py status
```

## Quick Start

### 1. Initial Setup

Run the setup wizard to configure your LLM providers:

```bash
arien setup
```

This will guide you through:
- Configuring Deepseek API key
- Setting up Ollama (if installed)
- Testing provider connections
- Setting default provider and model

### 2. Start Chatting

```bash
# Start with default provider
arien chat

# Specify provider and model
arien chat --provider deepseek --model deepseek-chat

# Set working directory
arien chat --dir /path/to/project
```

### 3. Basic Usage

Once in the chat interface:

```
You: List all Python files in the current directory

Arien: I'll help you list all Python files in the current directory.

🔧 Executing: execute_shell_command
Arguments: {
  "command": "find . -name '*.py' -type f",
  "mode": "sequential"
}

Command: find . -name '*.py' -type f | Exit Code: 0 | Time: 0.045s

Output:
./src/arien_cli/__init__.py
./src/arien_cli/main.py
./src/arien_cli/core/config.py
./src/arien_cli/core/session.py
...

I found several Python files in your current directory. The main application files are located in the `src/arien_cli/` directory structure.
```

## Configuration

### Provider Configuration

```bash
# List configured providers
arien config --list

# Configure Deepseek
arien config --set-provider deepseek --api-key YOUR_API_KEY

# Configure Ollama
arien config --set-provider ollama --base-url http://localhost:11434

# Show current configuration
arien config --show
```

### Provider Management

```bash
# Test provider connections
arien providers --test

# List available models for a provider
arien providers --list-models deepseek
arien providers --list-models ollama
```

## Shell Tool Usage

The shell tool provides comprehensive command execution with safety checks:

### Safety Levels

- **SAFE**: Read-only operations (ls, cat, grep, etc.)
- **MODERATE**: File operations, package management
- **DANGEROUS**: System modifications, user management
- **CRITICAL**: Potentially system-breaking operations (blocked)

### Execution Modes

#### Sequential Mode (Default)
Use when commands depend on previous results:
```
You: Install Python dependencies and then run tests
```

#### Parallel Mode
Use for independent operations:
```
You: Process all .txt files in parallel to count words
```

#### Interactive Mode
Use for commands requiring user input:
```
You: Run the interactive Python REPL
```

#### Background Mode
Use for long-running processes:
```
You: Start a development server in the background
```

### Examples

```bash
# File operations
You: Create a new directory called 'project' and navigate to it

# Package management
You: Install the latest version of requests using pip

# Git operations
You: Check git status and show recent commits

# System monitoring
You: Show current system resource usage

# Development tasks
You: Run the test suite and show coverage report

# Data processing
You: Find all log files and extract error messages
```

## Slash Commands

Use slash commands for quick access to features:

- `/help` - Show help information
- `/clear` - Clear chat history
- `/model` - Change current model
- `/provider` - Change current provider
- `/session` - Session management
- `/history` - Show message history
- `/exit` - Exit the application

## Advanced Features

### Error Handling and Recovery

Arien CLI includes sophisticated error handling:

- **Automatic Retry**: Failed operations are retried with exponential backoff
- **Error Classification**: Different error types receive appropriate handling
- **Recovery Suggestions**: Actionable suggestions for resolving issues
- **Graceful Degradation**: Continues operation when possible

### Session Management

- **Multiple Sessions**: Support for concurrent chat sessions
- **Session Persistence**: Save and restore conversation history
- **Context Switching**: Switch between different projects and contexts
- **Export/Import**: Share conversations and configurations

### Security Features

- **Command Validation**: Dangerous commands are flagged and require confirmation
- **Sandboxed Execution**: Commands run in controlled environments
- **Permission Checks**: Verify user permissions before execution
- **Audit Logging**: Comprehensive logging of all operations

## Development

### Project Structure

```
arien-cli/
├── src/arien_cli/
│   ├── core/                 # Core functionality
│   │   ├── llm_providers/    # LLM provider implementations
│   │   ├── tools/            # Tool system (shell, etc.)
│   │   ├── config.py         # Configuration management
│   │   ├── session.py        # Session management
│   │   └── error_handling.py # Error handling and retry logic
│   ├── ui/                   # User interface components
│   │   ├── components/       # Reusable UI components
│   │   └── terminal_ui.py    # Main terminal interface
│   ├── utils/                # Utility functions
│   └── main.py               # Application entry point
├── install.py                # Universal installer script
├── requirements.txt          # Python dependencies
├── pyproject.toml           # Project configuration
└── README.md                # This file
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

### Testing

```bash
# Install development dependencies
pip install -e ".[dev]"

# Run tests
pytest

# Run with coverage
pytest --cov=arien_cli

# Type checking
mypy src/arien_cli

# Code formatting
black src/arien_cli
isort src/arien_cli
```

## Troubleshooting

### Common Issues

#### Provider Connection Issues
```bash
# Test provider connections
arien providers --test

# Check configuration
arien config --show

# Reconfigure provider
arien config --set-provider deepseek --api-key NEW_KEY
```

#### Command Execution Issues
- Ensure proper permissions for the working directory
- Check if required tools are installed and in PATH
- Verify network connectivity for remote operations

#### Installation Issues
```bash
# Check installation status
python install.py status

# Reinstall
python install.py uninstall
python install.py install
```

### Getting Help

- Use `/help` in the chat interface for quick help
- Check the [GitHub Issues](https://github.com/arien-ai/arien-cli/issues) for known problems
- Join our [Discord community](https://discord.gg/arien-ai) for support

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Acknowledgments

- Built with [Rich](https://github.com/Textualize/rich) for beautiful terminal output
- Uses [Typer](https://github.com/tiangolo/typer) for CLI interface
- Powered by [Deepseek](https://platform.deepseek.com) and [Ollama](https://ollama.ai) for LLM capabilities
