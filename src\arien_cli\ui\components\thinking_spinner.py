"""
Thinking spinner component for Arien CLI.

This module provides an animated thinking indicator to show when the AI
is processing or generating responses.
"""

import asyncio
import time
from typing import Optional

from rich.align import Align
from rich.text import Text


class ThinkingSpinnerComponent:
    """
    Animated thinking spinner component.
    
    Provides visual feedback when the AI is:
    - Processing user input
    - Generating responses
    - Executing tools
    - Waiting for API responses
    """
    
    # Different spinner styles
    SPINNERS = {
        "dots": ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"],
        "braille": ["⣾", "⣽", "⣻", "⢿", "⡿", "⣟", "⣯", "⣷"],
        "line": ["-", "\\", "|", "/"],
        "arrow": ["←", "↖", "↑", "↗", "→", "↘", "↓", "↙"],
        "bounce": ["⠁", "⠂", "⠄", "⠂"],
        "pulse": ["●", "○", "●", "○"],
        "thinking": ["🤔", "💭", "🧠", "💡"],
        "working": ["⚙️ ", "🔧", "⚡", "✨"]
    }
    
    def __init__(self, style: str = "dots"):
        """
        Initialize thinking spinner.
        
        Args:
            style: Spinner style name
        """
        self.style = style
        self.frames = self.SPINNERS.get(style, self.SPINNERS["dots"])
        self.current_frame = 0
        self.is_active = False
        self.message = "Thinking..."
        self.start_time: Optional[float] = None
        self._task: Optional[asyncio.Task] = None
    
    def start(self, message: str = "Thinking...") -> None:
        """
        Start the spinner animation.
        
        Args:
            message: Message to display with spinner
        """
        self.message = message
        self.is_active = True
        self.start_time = time.time()
        self.current_frame = 0
    
    def stop(self) -> None:
        """Stop the spinner animation."""
        self.is_active = False
        self.start_time = None
        if self._task and not self._task.done():
            self._task.cancel()
    
    def next_frame(self) -> str:
        """
        Get the next animation frame.
        
        Returns:
            Current spinner frame
        """
        if not self.is_active:
            return ""
        
        frame = self.frames[self.current_frame]
        self.current_frame = (self.current_frame + 1) % len(self.frames)
        return frame
    
    def get_elapsed_time(self) -> str:
        """
        Get elapsed time since spinner started.
        
        Returns:
            Formatted elapsed time string
        """
        if not self.start_time:
            return ""
        
        elapsed = time.time() - self.start_time
        
        if elapsed < 60:
            return f"{elapsed:.1f}s"
        else:
            minutes = int(elapsed // 60)
            seconds = elapsed % 60
            return f"{minutes}m {seconds:.1f}s"
    
    def render(self) -> Text:
        """
        Render the spinner component.
        
        Returns:
            Rich Text object with spinner
        """
        if not self.is_active:
            return Text("")
        
        text = Text()
        
        # Add spinner frame
        spinner_frame = self.next_frame()
        text.append(spinner_frame, style="cyan bold")
        text.append(" ")
        
        # Add message
        text.append(self.message, style="cyan")
        
        # Add elapsed time if significant
        elapsed_time = self.get_elapsed_time()
        if elapsed_time and self.start_time and time.time() - self.start_time > 2:
            text.append(f" ({elapsed_time})", style="dim")
        
        return text
    
    def render_centered(self, width: int = 80) -> Text:
        """
        Render the spinner centered.
        
        Args:
            width: Terminal width for centering
            
        Returns:
            Centered Rich Text object
        """
        spinner_text = self.render()
        return Align.center(spinner_text, width=width)
    
    def set_style(self, style: str) -> None:
        """
        Change spinner style.
        
        Args:
            style: New spinner style name
        """
        if style in self.SPINNERS:
            self.style = style
            self.frames = self.SPINNERS[style]
            self.current_frame = 0
    
    def set_message(self, message: str) -> None:
        """
        Update spinner message.
        
        Args:
            message: New message to display
        """
        self.message = message
    
    async def animate_async(self, duration: float = 0.1) -> None:
        """
        Run async animation loop.
        
        Args:
            duration: Frame duration in seconds
        """
        while self.is_active:
            await asyncio.sleep(duration)
            if not self.is_active:
                break
    
    def get_available_styles(self) -> list:
        """
        Get list of available spinner styles.
        
        Returns:
            List of style names
        """
        return list(self.SPINNERS.keys())
    
    def preview_style(self, style: str, frames: int = 10) -> str:
        """
        Preview a spinner style.
        
        Args:
            style: Style to preview
            frames: Number of frames to show
            
        Returns:
            String showing animation frames
        """
        if style not in self.SPINNERS:
            return f"Style '{style}' not found"
        
        frames_list = self.SPINNERS[style]
        preview = []
        
        for i in range(min(frames, len(frames_list) * 2)):
            frame_index = i % len(frames_list)
            preview.append(frames_list[frame_index])
        
        return " ".join(preview)
    
    def __str__(self) -> str:
        """String representation of current spinner state."""
        if self.is_active:
            return f"ThinkingSpinner(active, style={self.style}, message='{self.message}')"
        else:
            return f"ThinkingSpinner(inactive, style={self.style})"
