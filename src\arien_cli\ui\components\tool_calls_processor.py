"""
Tool calls processing component for Arien CLI.

This module handles the execution and display of tool calls from LLM responses.
"""

import asyncio
import json
import logging
import time
from typing import Any, Dict, List, Optional, Tuple

from rich.console import Console, Group
from rich.live import Live
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, TimeElapsedColumn
from rich.syntax import Syntax
from rich.text import Text

from ...core.tools.tool_registry import tool_registry
from ...core.error_handling import error_handler

logger = logging.getLogger(__name__)


class ToolCallsProcessor:
    """
    Tool calls execution and display processor.
    
    Handles:
    - Tool call validation and execution
    - Real-time execution progress display
    - Result formatting and presentation
    - Error handling and recovery
    - Parallel and sequential execution
    """
    
    def __init__(self, console: Optional[Console] = None):
        """
        Initialize tool calls processor.
        
        Args:
            console: Rich console instance
        """
        self.console = console or Console()
        self.active_executions: Dict[str, Dict[str, Any]] = {}
        self.execution_history: List[Dict[str, Any]] = []
    
    async def process_tool_calls(
        self, 
        tool_calls: List[Dict[str, Any]], 
        show_progress: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Process a list of tool calls.
        
        Args:
            tool_calls: List of tool call dictionaries
            show_progress: Whether to show execution progress
            
        Returns:
            List of execution results
        """
        if not tool_calls:
            return []
        
        results = []
        
        # Determine execution mode based on tool calls
        execution_mode = self._determine_execution_mode(tool_calls)
        
        if execution_mode == "parallel" and len(tool_calls) > 1:
            results = await self._execute_parallel(tool_calls, show_progress)
        else:
            results = await self._execute_sequential(tool_calls, show_progress)
        
        # Store in history
        execution_record = {
            "timestamp": time.time(),
            "tool_calls": tool_calls,
            "results": results,
            "execution_mode": execution_mode,
            "total_time": sum(r.get("execution_time", 0) for r in results)
        }
        self.execution_history.append(execution_record)
        
        return results
    
    def _determine_execution_mode(self, tool_calls: List[Dict[str, Any]]) -> str:
        """
        Determine whether tool calls should be executed in parallel or sequential.
        
        Args:
            tool_calls: List of tool calls
            
        Returns:
            Execution mode: "parallel" or "sequential"
        """
        # For now, we'll use sequential execution for safety
        # In the future, this could analyze dependencies between commands
        
        # Check if any tool calls have dependencies
        for tool_call in tool_calls:
            if tool_call.get("name") == "execute_shell_command":
                args = tool_call.get("arguments", {})
                mode = args.get("mode", "sequential")
                if mode == "sequential":
                    return "sequential"
        
        # If all are independent operations, use parallel
        return "parallel" if len(tool_calls) > 1 else "sequential"
    
    async def _execute_sequential(
        self, 
        tool_calls: List[Dict[str, Any]], 
        show_progress: bool
    ) -> List[Dict[str, Any]]:
        """Execute tool calls sequentially."""
        results = []
        
        if show_progress and len(tool_calls) > 1:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                TimeElapsedColumn(),
                console=self.console
            ) as progress:
                
                overall_task = progress.add_task(
                    f"Executing {len(tool_calls)} tool calls...", 
                    total=len(tool_calls)
                )
                
                for i, tool_call in enumerate(tool_calls):
                    tool_name = tool_call.get("name", "unknown")
                    progress.update(overall_task, description=f"Executing {tool_name}...")
                    
                    result = await self._execute_single_tool_call(tool_call, progress)
                    results.append(result)
                    
                    progress.advance(overall_task)
        else:
            # Execute without progress display
            for tool_call in tool_calls:
                result = await self._execute_single_tool_call(tool_call)
                results.append(result)
        
        return results
    
    async def _execute_parallel(
        self, 
        tool_calls: List[Dict[str, Any]], 
        show_progress: bool
    ) -> List[Dict[str, Any]]:
        """Execute tool calls in parallel."""
        if show_progress:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                TimeElapsedColumn(),
                console=self.console
            ) as progress:
                
                # Create tasks for each tool call
                tasks = []
                progress_tasks = []
                
                for tool_call in tool_calls:
                    tool_name = tool_call.get("name", "unknown")
                    task_id = progress.add_task(f"Executing {tool_name}...", total=None)
                    progress_tasks.append(task_id)
                    
                    task = asyncio.create_task(
                        self._execute_single_tool_call(tool_call, progress, task_id)
                    )
                    tasks.append(task)
                
                # Wait for all tasks to complete
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Handle exceptions
                processed_results = []
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        processed_results.append({
                            "success": False,
                            "error": str(result),
                            "tool_call": tool_calls[i]
                        })
                    else:
                        processed_results.append(result)
                
                return processed_results
        else:
            # Execute without progress display
            tasks = [
                asyncio.create_task(self._execute_single_tool_call(tool_call))
                for tool_call in tool_calls
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Handle exceptions
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append({
                        "success": False,
                        "error": str(result),
                        "tool_call": tool_calls[i]
                    })
                else:
                    processed_results.append(result)
            
            return processed_results
    
    async def _execute_single_tool_call(
        self, 
        tool_call: Dict[str, Any], 
        progress: Optional[Progress] = None,
        task_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Execute a single tool call.
        
        Args:
            tool_call: Tool call dictionary
            progress: Optional progress instance
            task_id: Optional progress task ID
            
        Returns:
            Execution result
        """
        tool_name = tool_call.get("name", "")
        arguments = tool_call.get("arguments", {})
        call_id = tool_call.get("id", f"call_{int(time.time())}")
        
        # Track execution
        execution_info = {
            "tool_name": tool_name,
            "arguments": arguments,
            "start_time": time.time(),
            "status": "running"
        }
        self.active_executions[call_id] = execution_info
        
        try:
            # Execute the tool
            result = await tool_registry.execute_tool(tool_name, arguments)
            
            execution_info["status"] = "completed"
            execution_info["end_time"] = time.time()
            execution_info["execution_time"] = execution_info["end_time"] - execution_info["start_time"]
            execution_info["result"] = result
            
            # Update progress if available
            if progress and task_id is not None:
                status = "✅" if result.get("success", False) else "❌"
                progress.update(task_id, description=f"{status} {tool_name}")
            
            return result
        
        except Exception as e:
            execution_info["status"] = "failed"
            execution_info["end_time"] = time.time()
            execution_info["execution_time"] = execution_info["end_time"] - execution_info["start_time"]
            execution_info["error"] = str(e)
            
            # Update progress if available
            if progress and task_id is not None:
                progress.update(task_id, description=f"❌ {tool_name} (failed)")
            
            logger.error(f"Tool execution failed: {tool_name} - {e}")
            
            return {
                "success": False,
                "error": str(e),
                "tool_name": tool_name,
                "arguments": arguments,
                "execution_time": execution_info["execution_time"]
            }
        
        finally:
            # Clean up tracking
            if call_id in self.active_executions:
                del self.active_executions[call_id]
    
    def render_tool_call_preview(self, tool_call: Dict[str, Any]) -> Panel:
        """
        Render a preview of a tool call before execution.
        
        Args:
            tool_call: Tool call dictionary
            
        Returns:
            Rich Panel with tool call preview
        """
        tool_name = tool_call.get("name", "unknown")
        arguments = tool_call.get("arguments", {})
        
        # Create preview content
        preview_text = Text()
        preview_text.append("🔧 Tool Call: ", style="yellow bold")
        preview_text.append(tool_name, style="cyan bold")
        preview_text.append("\n\n")
        
        # Format arguments
        if arguments:
            preview_text.append("Arguments:\n", style="white bold")
            
            # Pretty print JSON arguments
            try:
                args_json = json.dumps(arguments, indent=2)
                syntax = Syntax(args_json, "json", theme="monokai", line_numbers=False)
                
                return Panel(
                    Group(preview_text, syntax),
                    title=f"Tool Call: {tool_name}",
                    border_style="yellow",
                    padding=(0, 1)
                )
            except Exception:
                # Fallback to simple text
                for key, value in arguments.items():
                    preview_text.append(f"  {key}: ", style="dim")
                    preview_text.append(str(value), style="white")
                    preview_text.append("\n")
        
        return Panel(
            preview_text,
            title=f"Tool Call: {tool_name}",
            border_style="yellow",
            padding=(0, 1)
        )
    
    def render_execution_result(self, result: Dict[str, Any]) -> Panel:
        """
        Render the result of a tool execution.
        
        Args:
            result: Tool execution result
            
        Returns:
            Rich Panel with execution result
        """
        success = result.get("success", False)
        tool_name = result.get("tool_name", "unknown")
        execution_time = result.get("execution_time", 0)
        
        # Create result content
        result_text = Text()
        
        # Status indicator
        if success:
            result_text.append("✅ Success", style="green bold")
        else:
            result_text.append("❌ Failed", style="red bold")
        
        result_text.append(f" | Time: {execution_time:.3f}s", style="dim")
        result_text.append("\n\n")
        
        # Add result details
        if success:
            # Show command output for shell commands
            if tool_name == "execute_shell_command":
                command = result.get("command", "")
                stdout = result.get("stdout", "").strip()
                stderr = result.get("stderr", "").strip()
                exit_code = result.get("exit_code", 0)
                
                result_text.append(f"Command: ", style="white bold")
                result_text.append(command, style="cyan")
                result_text.append(f"\nExit Code: {exit_code}\n\n", style="dim")
                
                if stdout:
                    result_text.append("Output:\n", style="white bold")
                    # Truncate long output
                    if len(stdout) > 1000:
                        stdout = stdout[:1000] + "\n... (truncated)"
                    result_text.append(stdout, style="white")
                    result_text.append("\n")
                
                if stderr:
                    result_text.append("Errors:\n", style="red bold")
                    result_text.append(stderr, style="red")
            else:
                # Generic result display
                result_data = result.get("result", "")
                if result_data:
                    result_text.append("Result:\n", style="white bold")
                    result_text.append(str(result_data), style="white")
        else:
            # Show error information
            error = result.get("error", "Unknown error")
            result_text.append("Error: ", style="red bold")
            result_text.append(error, style="red")
        
        border_style = "green" if success else "red"
        title = f"Tool Result: {tool_name}"
        
        return Panel(
            result_text,
            title=title,
            border_style=border_style,
            padding=(0, 1)
        )
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """
        Get summary of recent tool executions.
        
        Returns:
            Execution summary dictionary
        """
        if not self.execution_history:
            return {
                "total_executions": 0,
                "success_rate": 0,
                "average_time": 0,
                "most_used_tools": []
            }
        
        # Calculate statistics
        total_executions = len(self.execution_history)
        successful_executions = sum(
            1 for record in self.execution_history
            if all(r.get("success", False) for r in record["results"])
        )
        success_rate = (successful_executions / total_executions) * 100
        
        total_time = sum(record["total_time"] for record in self.execution_history)
        average_time = total_time / total_executions
        
        # Count tool usage
        tool_counts = {}
        for record in self.execution_history:
            for tool_call in record["tool_calls"]:
                tool_name = tool_call.get("name", "unknown")
                tool_counts[tool_name] = tool_counts.get(tool_name, 0) + 1
        
        most_used_tools = sorted(tool_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            "total_executions": total_executions,
            "success_rate": round(success_rate, 1),
            "average_time": round(average_time, 3),
            "most_used_tools": most_used_tools
        }
    
    def clear_history(self) -> None:
        """Clear execution history."""
        self.execution_history.clear()
    
    def get_active_executions(self) -> Dict[str, Dict[str, Any]]:
        """Get currently active executions."""
        return self.active_executions.copy()
