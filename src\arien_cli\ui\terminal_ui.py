"""
Main terminal UI for Arien CLI.

This module provides the main terminal interface that orchestrates
all UI components and handles user interactions.
"""

import asyncio
import logging
import os
import signal
import sys
from typing import Any, Dict, Optional

from prompt_toolkit import Application
from prompt_toolkit.application import get_app
from prompt_toolkit.key_binding import KeyBindings
from prompt_toolkit.layout import Layout
from prompt_toolkit.layout.containers import HSplit, VSplit, Window
from prompt_toolkit.layout.controls import FormattedTextControl
from prompt_toolkit.shortcuts import input_dialog, message_dialog
from prompt_toolkit.widgets import TextArea
from rich.console import Console
from rich.live import Live

from ..core.config import config_manager
from ..core.session import session_manager
from ..core.tools.tool_registry import tool_registry
from .components.terminal_layout import TerminalLayoutComponent
from .components.slash_commands import SlashCommandsComponent
from .components.message_history import MessageHistoryComponent
from .components.tool_calls_processor import ToolCallsProcessor
from ..utils.output_formatter import output_formatter
from .. import SYSTEM_PROMPT

logger = logging.getLogger(__name__)


class TerminalUI:
    """
    Main terminal UI application.
    
    Provides:
    - Interactive chat interface
    - Real-time streaming responses
    - Command execution and monitoring
    - Session management
    - Error handling and recovery
    """
    
    def __init__(self, console: Optional[Console] = None):
        """
        Initialize terminal UI.

        Args:
            console: Rich console instance
        """
        self.console = console or Console()
        self.layout_component = TerminalLayoutComponent(self.console)
        self.slash_commands = SlashCommandsComponent(self.console)
        self.message_history = MessageHistoryComponent()
        self.tool_processor = ToolCallsProcessor(self.console)

        self.current_session = None
        self.is_running = False
        self.input_buffer = ""
        self.streaming_content = ""
        self.current_message_id = None

        # Setup signal handlers
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down...")
            self.is_running = False
            if self.current_session:
                asyncio.create_task(self.current_session.close())
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def run(
        self,
        provider: str = "deepseek",
        model: str = "deepseek-chat",
        working_directory: Optional[str] = None
    ) -> None:
        """
        Run the terminal UI.
        
        Args:
            provider: LLM provider to use
            model: Model to use
            working_directory: Working directory for commands
        """
        self.is_running = True
        
        try:
            # Initialize session
            await self._initialize_session(provider, model, working_directory)
            
            # Start UI
            await self._run_ui()
        
        except KeyboardInterrupt:
            self.console.print("\n[yellow]Interrupted by user[/yellow]")
        except Exception as e:
            logger.error(f"UI error: {e}")
            self.console.print(f"[red]Error: {e}[/red]")
        finally:
            await self._cleanup()
    
    async def _initialize_session(
        self,
        provider: str,
        model: str,
        working_directory: Optional[str]
    ) -> None:
        """Initialize chat session."""
        self.console.print("Initializing session...", style="dim")
        
        # Create session
        self.current_session = session_manager.create_session(
            provider_name=provider,
            model=model,
            working_directory=working_directory or os.getcwd()
        )
        
        # Initialize provider
        await self.current_session.initialize_provider()

        # Add system prompt to session
        self.current_session.add_message("system", SYSTEM_PROMPT)

        # Update UI
        self.layout_component.update_header(
            session_id=self.current_session.id,
            provider=provider,
            model=model,
            working_directory=self.current_session.working_directory
        )

        # Show welcome message
        self._show_welcome_message()

        self.console.print("[green]Session initialized![/green]")
    
    def _show_welcome_message(self) -> None:
        """Show welcome message."""
        welcome_msg = f"""Welcome to Arien CLI! 🚀

I'm your AI assistant with shell command execution capabilities.
I can help you with:
• File system operations and navigation
• Package management and software installation  
• Development tasks (git, build tools, testing)
• System administration and monitoring
• Data processing and analysis

Type your message below or use these commands:
• /help - Show help information
• /model - Change model
• /provider - Change provider
• /session - Session management
• /clear - Clear chat history
• /exit - Exit the application

Let's get started! What would you like to do?"""
        
        self.layout_component.add_message("assistant", welcome_msg)
    
    async def _run_ui(self) -> None:
        """Run the main UI loop."""
        with Live(
            self.layout_component.render(),
            console=self.console,
            refresh_per_second=10,
            screen=True
        ) as live:
            
            while self.is_running:
                try:
                    # Get user input
                    user_input = await self._get_user_input()
                    
                    if not user_input.strip():
                        continue
                    
                    # Handle slash commands
                    if self.slash_commands.is_slash_command(user_input):
                        await self._handle_slash_command(user_input, live)
                        continue
                    
                    # Add user message to UI
                    self.layout_component.add_message("user", user_input)
                    live.update(self.layout_component.render())
                    
                    # Start thinking animation
                    self.layout_component.start_thinking("Processing your request...")
                    live.update(self.layout_component.render())
                    
                    # Send message to AI
                    await self._process_user_message(user_input, live)
                
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    logger.error(f"Error in UI loop: {e}")
                    self.layout_component.show_error(str(e))
                    live.update(self.layout_component.render())
    
    async def _get_user_input(self) -> str:
        """Get user input asynchronously."""
        # This is a simplified implementation
        # In a real implementation, you'd use prompt_toolkit for better input handling
        return await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: input("You: ")
        )
    
    async def _process_user_message(self, message: str, live) -> None:
        """Process user message and stream response."""
        try:
            self.streaming_content = ""

            # Get tools for the session
            tools = tool_registry.get_tool_definitions()

            # Send message with streaming
            async for chunk in self.current_session.send_message(
                message,
                tools=tools,
                stream=True
            ):
                chunk_type = chunk.get("type")

                if chunk_type == "content":
                    content = chunk.get("content", "")
                    self.streaming_content += content

                    # Update UI with streaming content
                    self.layout_component.stop_thinking()
                    formatted_content = output_formatter.format_streaming_content(
                        self.streaming_content,
                        is_complete=False
                    )
                    self.layout_component.update_streaming_message(formatted_content)
                    live.update(self.layout_component.render())

                elif chunk_type == "tool_calls":
                    tool_calls = chunk.get("tool_calls", [])

                    # Show tool call previews
                    for tool_call in tool_calls:
                        preview = self.tool_processor.render_tool_call_preview(tool_call)
                        self.layout_component.add_tool_preview(preview)

                    live.update(self.layout_component.render())

                    # Execute tool calls
                    results = await self.tool_processor.process_tool_calls(
                        tool_calls,
                        show_progress=True
                    )

                    # Show results
                    for result in results:
                        result_display = self.tool_processor.render_execution_result(result)
                        self.layout_component.add_tool_result(result_display)

                    live.update(self.layout_component.render())

                elif chunk_type == "error":
                    error_content = chunk.get("content", "Unknown error")
                    self.layout_component.show_error(error_content)
                    live.update(self.layout_component.render())

                elif chunk_type == "finish":
                    self.layout_component.stop_thinking()

                    # Finalize streaming content
                    if self.streaming_content:
                        final_content = output_formatter.format_streaming_content(
                            self.streaming_content,
                            is_complete=True
                        )
                        self.layout_component.finalize_streaming_message(final_content)

                    live.update(self.layout_component.render())
                    break

        except Exception as e:
            logger.error(f"Error processing message: {e}")
            self.layout_component.stop_thinking()
            self.layout_component.show_error(f"Error: {e}")
            live.update(self.layout_component.render())
    
    async def _handle_slash_command(self, command: str, live) -> None:
        """Handle slash commands."""
        try:
            result = await self.slash_commands.execute_command(command)

            if result["success"]:
                command_result = result["result"]

                # Handle special commands
                if result["command"] == "exit":
                    self.is_running = False
                    return
                elif result["command"] == "clear":
                    self.layout_component.clear_chat()
                    live.update(self.layout_component.render())
                    return

                # Display command result
                if isinstance(command_result, str):
                    self.layout_component.add_message("system", command_result)
                else:
                    formatted_result = output_formatter.format_json(command_result)
                    self.layout_component.add_message("system", formatted_result)
            else:
                # Show error
                error_msg = result.get("error", "Command failed")
                suggestions = result.get("suggestions", [])

                if suggestions:
                    error_msg += f"\n\nSuggestions: {', '.join(suggestions)}"

                self.layout_component.show_error(error_msg)

            live.update(self.layout_component.render())

        except Exception as e:
            logger.error(f"Error handling slash command: {e}")
            self.layout_component.show_error(f"Command error: {e}")
            live.update(self.layout_component.render())
    
    def _show_help(self) -> None:
        """Show help information."""
        help_text = """Available Commands:

/help - Show this help message
/clear - Clear chat history
/model - Change current model
/provider - Change current provider
/session - Session management
/exit - Exit the application

AI Capabilities:
• Execute shell commands safely
• File system operations
• Package management
• Development tasks
• System monitoring
• Data processing

For more information, visit: https://github.com/arien-ai/arien-cli"""
        
        self.layout_component.show_help(help_text)
    
    async def _change_model(self) -> None:
        """Change current model."""
        # This would show a model selection dialog
        # For now, just show a message
        self.layout_component.add_message(
            "system",
            "Model changing not implemented in this demo"
        )
    
    async def _change_provider(self) -> None:
        """Change current provider."""
        # This would show a provider selection dialog
        # For now, just show a message
        self.layout_component.add_message(
            "system",
            "Provider changing not implemented in this demo"
        )
    
    async def _manage_session(self) -> None:
        """Manage sessions."""
        # This would show session management interface
        # For now, just show current session info
        session_info = self.current_session.get_session_info()
        info_text = f"""Current Session:
ID: {session_info.id}
Provider: {session_info.provider}
Model: {session_info.model}
Messages: {session_info.message_count}
Working Directory: {session_info.working_directory}"""
        
        self.layout_component.add_message("system", info_text)
    
    async def _cleanup(self) -> None:
        """Cleanup resources."""
        if self.current_session:
            await self.current_session.close()
        
        logger.info("UI cleanup completed")
